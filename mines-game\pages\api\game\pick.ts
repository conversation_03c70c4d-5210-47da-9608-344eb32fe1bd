import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { revealCell } from '@/lib/game-logic';
import { initDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_id, cell_index } = req.body;

    // Validate input
    if (typeof game_id !== 'number' || game_id <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid game ID'
      });
    }

    if (typeof cell_index !== 'number' || cell_index < 0 || cell_index >= 25) {
      return res.status(400).json({
        success: false,
        error: 'Invalid cell index'
      });
    }

    const result = await reveal<PERSON>ell(game_id, cell_index, user.id);

    if (result.success) {
      return res.status(200).json({
        success: true,
        hit: result.hit,
        multiplier: result.multiplier,
        gameOver: result.gameOver,
        profit: result.profit,
        minePositions: result.minePositions, // Include mine positions when game is over
        message: result.hit ? 'Mine hit! Game over.' : 'Safe cell revealed!'
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.error || 'Failed to reveal cell'
      });
    }
  } catch (error) {
    console.error('Reveal cell API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
