import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format currency values with proper decimal places
 */
export function formatCurrency(amount: number, currency: 'USDT' | 'LTC' = 'USDT', compact: boolean = false): string {
  if (compact && Math.abs(amount) >= 1000) {
    return formatNumber(amount);
  }
  const decimals = currency === 'USDT' ? 2 : 8;
  return amount.toFixed(decimals);
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatNumber(num: number): string {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1) + 'B';
  }
  if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + 'M';
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + 'K';
  }
  return num.toString();
}

/**
 * Generate a random string for client seeds
 */
export function generateClientSeed(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 */
export function isValidPassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

/**
 * Calculate multiplier based on mines and revealed cells
 */
export function calculateMultiplier(mineCount: number, revealedCount: number, gridSize: number = 25): number {
  if (revealedCount === 0) return 1;

  const safeCells = gridSize - mineCount;
  const remainingSafeCells = safeCells - revealedCount;

  if (remainingSafeCells <= 0) return 1;

  // Base multiplier calculation with house edge
  const baseMultiplier = safeCells / remainingSafeCells;
  const houseEdge = 0.04; // 4% house edge

  return Math.max(1, baseMultiplier * (1 - houseEdge));
}

/**
 * Calculate potential profit
 */
export function calculateProfit(betAmount: number, multiplier: number): number {
  return betAmount * multiplier - betAmount;
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Sleep utility for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate a random integer between min and max (inclusive)
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Shuffle array using Fisher-Yates algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Format date to readable string
 */
export function formatDate(date: string | Date): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Failed to copy text: ', err);
    return false;
  }
}

/**
 * Game configuration constants
 */
export const GAME_CONFIG = {
  GRID_SIZE: 25,
  MIN_MINES: 1,
  MAX_MINES: 24,
  MIN_BET: 0.01,
  MAX_BET: 1000,
  HOUSE_EDGE: 0.04, // 4%
  BASE_MULTIPLIER: 1.0
} as const;

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    SIGNUP: '/api/auth/signup',
    ME: '/api/auth/me',
    LOGOUT: '/api/auth/logout'
  },
  GAME: {
    START: '/api/game/start',
    MOVE: '/api/game/move',
    CASHOUT: '/api/game/cashout',
    HISTORY: '/api/game/history',
    ACTIVE: '/api/game/active',
    CONFIG: '/api/game/config',
    LIST: '/api/game/list',
    STATS: '/api/game/stats'
  },
  // Legacy endpoints for backward compatibility
  MINES: {
    START: '/api/game/start',
    PICK: '/api/game/move',
    CASHOUT: '/api/game/cashout',
    HISTORY: '/api/game/history'
  },
  WALLET: {
    DEPOSIT: '/api/wallet/deposit',
    WITHDRAW: '/api/wallet/withdraw',
    BALANCE: '/api/wallet/balance'
  }
} as const;

/**
 * Session storage utilities for tracking current session stats
 */
export const SessionStorage = {
  SESSION_KEY: 'betoctave_session_stats',

  /**
   * Get current session data
   */
  getSession: () => {
    if (typeof window === 'undefined') return null;

    try {
      const sessionData = localStorage.getItem(SessionStorage.SESSION_KEY);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Error reading session data:', error);
      return null;
    }
  },

  /**
   * Initialize or reset session
   */
  resetSession: () => {
    if (typeof window === 'undefined') return;

    const sessionData = {
      startTime: new Date().toISOString(),
      resetCount: (SessionStorage.getSession()?.resetCount || 0) + 1
    };

    try {
      localStorage.setItem(SessionStorage.SESSION_KEY, JSON.stringify(sessionData));
    } catch (error) {
      console.error('Error saving session data:', error);
    }
  },

  /**
   * Get session start time
   */
  getSessionStartTime: () => {
    const session = SessionStorage.getSession();
    if (session?.startTime) {
      return session.startTime;
    }
    // If no session exists, create one and return its start time
    SessionStorage.resetSession();
    return SessionStorage.getSession()?.startTime || new Date().toISOString();
  },

  /**
   * Check if session exists
   */
  hasSession: () => {
    return SessionStorage.getSession() !== null;
  },

  /**
   * Initialize session if it doesn't exist
   */
  initializeSession: () => {
    if (!SessionStorage.hasSession()) {
      console.log('📊 Initializing new session');
      SessionStorage.resetSession();
    } else {
      console.log('📊 Session already exists:', SessionStorage.getSession());
    }
  }
};