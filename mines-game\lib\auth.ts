import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { User } from '@/types';
import { userDb } from './database';

// JWT secret - in production, this should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
const JWT_EXPIRES_IN = '7d';

/**
 * Hash password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

/**
 * Verify password against hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

/**
 * Generate JWT token for user
 */
export function generateToken(user: User): string {
  const payload = {
    userId: user.id,
    username: user.username,
    email: user.email
  };
  
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

/**
 * Verify JWT token and return user data
 */
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
}

/**
 * Extract token from request headers
 */
export function extractTokenFromRequest(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check cookies for token
  const cookieToken = req.cookies.token;
  if (cookieToken) {
    return cookieToken;
  }
  
  return null;
}

/**
 * Middleware to authenticate requests
 */
export function authenticateRequest(req: NextApiRequest): User | null {
  const token = extractTokenFromRequest(req);
  
  if (!token) {
    return null;
  }
  
  const decoded = verifyToken(token);
  if (!decoded || !decoded.userId) {
    return null;
  }
  
  // Get fresh user data from database
  const user = userDb.findById(decoded.userId);
  return user;
}

/**
 * Higher-order function to protect API routes
 */
export function withAuth(handler: (req: NextApiRequest, res: NextApiResponse, user: User) => Promise<void>) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const user = authenticateRequest(req);
      
      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }
      
      await handler(req, res, user);
    } catch (error) {
      console.error('Auth middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}

/**
 * Validate user registration data
 */
export function validateRegistrationData(username: string, email: string, password: string): string | null {
  // Username validation
  if (!username || username.length < 3 || username.length > 20) {
    return 'Username must be between 3 and 20 characters';
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return 'Username can only contain letters, numbers, and underscores';
  }
  
  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email || !emailRegex.test(email)) {
    return 'Please provide a valid email address';
  }
  
  // Password validation
  if (!password || password.length < 8) {
    return 'Password must be at least 8 characters long';
  }
  
  if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
    return 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
  }
  
  return null;
}

/**
 * Validate login data
 */
export function validateLoginData(email: string, password: string): string | null {
  if (!email || !password) {
    return 'Email and password are required';
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Please provide a valid email address';
  }
  
  return null;
}

/**
 * Register new user
 */
export async function registerUser(username: string, email: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> {
  try {
    // Validate input data
    const validationError = validateRegistrationData(username, email, password);
    if (validationError) {
      return { success: false, error: validationError };
    }
    
    // Check if user already exists
    const existingUserByEmail = userDb.findByEmail(email);
    if (existingUserByEmail) {
      return { success: false, error: 'Email already registered' };
    }
    
    const existingUserByUsername = userDb.findByUsername(username);
    if (existingUserByUsername) {
      return { success: false, error: 'Username already taken' };
    }
    
    // Hash password
    const passwordHash = await hashPassword(password);
    
    // Create user
    const user = userDb.create(username, email, passwordHash);
    
    // Remove password hash from response
    const { password_hash, ...userWithoutPassword } = user;
    
    return { success: true, user: userWithoutPassword as User };
  } catch (error) {
    console.error('Registration error:', error);
    return { success: false, error: 'Failed to register user' };
  }
}

/**
 * Login user
 */
export async function loginUser(email: string, password: string): Promise<{ success: boolean; user?: User; token?: string; error?: string }> {
  try {
    // Validate input data
    const validationError = validateLoginData(email, password);
    if (validationError) {
      return { success: false, error: validationError };
    }
    
    // Find user by email
    const user = userDb.findByEmail(email);
    if (!user) {
      return { success: false, error: 'Invalid email or password' };
    }
    
    // Verify password
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return { success: false, error: 'Invalid email or password' };
    }
    
    // Generate token
    const token = generateToken(user);
    
    // Remove password hash from response
    const { password_hash, ...userWithoutPassword } = user;
    
    return { 
      success: true, 
      user: userWithoutPassword as User, 
      token 
    };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, error: 'Failed to login' };
  }
}

/**
 * Set authentication cookie
 */
export function setAuthCookie(res: NextApiResponse, token: string): void {
  const isProduction = process.env.NODE_ENV === 'production';
  
  res.setHeader('Set-Cookie', [
    `token=${token}; HttpOnly; Path=/; Max-Age=${7 * 24 * 60 * 60}; SameSite=Strict${isProduction ? '; Secure' : ''}`
  ]);
}

/**
 * Clear authentication cookie
 */
export function clearAuthCookie(res: NextApiResponse): void {
  res.setHeader('Set-Cookie', [
    'token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict'
  ]);
}

/**
 * Rate limiting for authentication endpoints
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(ip: string, maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (record.count >= maxAttempts) {
    return false;
  }
  
  record.count++;
  return true;
}

/**
 * Get client IP address
 */
export function getClientIP(req: NextApiRequest): string {
  const forwarded = req.headers['x-forwarded-for'];
  const ip = forwarded ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]) : req.socket.remoteAddress;
  return ip || 'unknown';
}
