import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Gem, Zap, Shield, TrendingUp } from 'lucide-react';

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push('/lobby');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  if (user) {
    return null; // Will redirect to /lobby
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Gem className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">BetOctave</span>
          </div>
          <div className="space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push('/login')}
              className="border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white"
            >
              Login
            </Button>
            <Button
              onClick={() => router.push('/signup')}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Sign Up
            </Button>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold text-white mb-6">
            Provably Fair
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              {" "}Gambling
            </span>
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Experience the thrill of crypto gambling with our provably fair gaming platform.
            Play multiple games, choose your risk, and win big with transparent mechanics!
          </p>
          <div className="space-x-4">
            <Button
              size="lg"
              onClick={() => router.push('/signup')}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg"
            >
              Start Playing
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => router.push('/login')}
              className="border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-3 text-lg"
            >
              Login
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <Shield className="h-8 w-8 text-green-400 mb-2" />
              <CardTitle className="text-white">Provably Fair</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-gray-300">
                Verify every game result with our transparent cryptographic system
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <Zap className="h-8 w-8 text-yellow-400 mb-2" />
              <CardTitle className="text-white">Instant Payouts</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-gray-300">
                Cash out instantly with USDT and LTC support
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <TrendingUp className="h-8 w-8 text-blue-400 mb-2" />
              <CardTitle className="text-white">Multiple Games</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-gray-300">
                Enjoy various gambling games with different risk levels and rewards
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <Gem className="h-8 w-8 text-purple-400 mb-2" />
              <CardTitle className="text-white">Mobile Ready</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-gray-300">
                Play anywhere with our responsive mobile-first design
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* How to Play */}
        <div className="text-center">
          <h2 className="text-4xl font-bold text-white mb-8">How to Play</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Choose Your Game</h3>
              <p className="text-gray-300">
                Select from our variety of games and set your bet amount
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Play & Win</h3>
              <p className="text-gray-300">
                Make your moves and watch your multiplier grow with each success
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-white">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Cash Out</h3>
              <p className="text-gray-300">
                Cash out anytime to secure your winnings before it's too late
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="container mx-auto px-4 py-8 border-t border-gray-800">
        <div className="text-center text-gray-400">
          <p>&copy; 2024 BetOctave. All rights reserved. Play responsibly.</p>
        </div>
      </footer>
    </div>
  );
}
