/**
 * Test script for Phase 2 database schema enhancement
 */

const { initDatabase, userStatsDb, leaderboardDb, gameSessionDb } = require('./lib/database.ts');

async function testPhase2Database() {
  console.log('🧪 Testing Phase 2 Database Schema Enhancement...\n');

  try {
    // Initialize database
    const db = initDatabase();
    console.log('✅ Database initialized successfully');

    // Test if new tables exist
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN (
        'user_statistics', 'leaderboards', 'game_sessions', 
        'achievements', 'user_achievements'
      )
    `).all();

    console.log('\n📋 Phase 2 Tables Created:');
    tables.forEach(table => {
      console.log(`  ✅ ${table.name}`);
    });

    if (tables.length === 5) {
      console.log('\n🎉 All Phase 2 tables created successfully!');
    } else {
      console.log(`\n⚠️  Only ${tables.length}/5 tables created`);
    }

    // Test user statistics operations
    console.log('\n🔍 Testing User Statistics Operations...');
    
    // Create a test user stat entry
    try {
      const testStat = userStatsDb.create(1, 'mines');
      console.log('  ✅ User statistics creation works');
    } catch (error) {
      console.log('  ❌ User statistics creation failed:', error.message);
    }

    // Test leaderboard operations
    console.log('\n🏆 Testing Leaderboard Operations...');
    
    try {
      const leaderboardUpdate = leaderboardDb.updateEntry(1, 'testuser', 'mines', 'profit', 100.50, 'daily');
      console.log('  ✅ Leaderboard update works');
    } catch (error) {
      console.log('  ❌ Leaderboard update failed:', error.message);
    }

    // Test game session operations
    console.log('\n🎮 Testing Game Session Operations...');
    
    try {
      const session = gameSessionDb.startSession(1);
      console.log('  ✅ Game session creation works');
    } catch (error) {
      console.log('  ❌ Game session creation failed:', error.message);
    }

    console.log('\n✨ Phase 2 Database Schema Enhancement Test Complete!');

  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

// Run the test
testPhase2Database();
