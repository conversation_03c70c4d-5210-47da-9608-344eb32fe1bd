import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb, userDb } from '@/lib/database';
import { gameFactory } from '@/lib/games/GameFactory';
import { GameActionType } from '@/lib/games/BaseGameProvider';
import { GameType } from '@/types';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_id, game_type, ...moveParams } = req.body;

    // Validate input
    if (!game_id || typeof game_id !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'Game ID is required'
      });
    }

    if (!game_type || typeof game_type !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Game type is required'
      });
    }

    // Get game from database
    const game = gameDb.findById(game_id);
    if (!game) {
      return res.status(404).json({
        success: false,
        error: 'Game not found'
      });
    }

    // Verify game belongs to user
    if (game.user_id !== user.id) {
      return res.status(403).json({
        success: false,
        error: 'Unauthorized'
      });
    }

    // Check if game is active
    if (game.status !== 'active') {
      return res.status(400).json({
        success: false,
        error: 'Game is not active'
      });
    }

    // Verify game type matches
    if (game.game_type !== game_type) {
      return res.status(400).json({
        success: false,
        error: 'Game type mismatch'
      });
    }

    // Ensure game factory is initialized
    await gameFactory.initialize();

    // Process the move using game factory
    const moveResult = await gameFactory.processGameAction(
      game_type as GameType,
      game,
      GameActionType.MAKE_MOVE,
      moveParams
    );

    if (!moveResult.success || !moveResult.gameState) {
      return res.status(400).json({
        success: false,
        error: moveResult.error || 'Move failed'
      });
    }

    const updatedGame = moveResult.gameState;

    // Update game in database
    gameDb.update(game_id, updatedGame);

    // If game ended with a win, update user balance
    if (updatedGame.status === 'won' || updatedGame.status === 'cashed_out') {
      if (updatedGame.profit > 0) {
        const newBalance = user.usdt_balance + updatedGame.bet_amount + updatedGame.profit;
        userDb.updateBalance(user.id, 'USDT', newBalance);
      }
    }

    // Prepare response data
    const responseData: any = {
      success: true,
      gameState: updatedGame,
      gameOver: updatedGame.status !== 'active'
    };

    // Add game-specific response data
    if (game_type === 'mines') {
      const hitMine = moveParams.cellIndex !== undefined && 
                     updatedGame.mine_positions?.includes(moveParams.cellIndex);
      
      responseData.hit = hitMine;
      responseData.multiplier = updatedGame.current_multiplier;
      responseData.profit = updatedGame.profit;
      
      // Include mine positions if game is over
      if (updatedGame.status !== 'active') {
        responseData.minePositions = updatedGame.mine_positions;
      }
    } else if (game_type === 'dice') {
      responseData.result = (updatedGame as any).result;
      responseData.won = updatedGame.status === 'won';
      responseData.multiplier = updatedGame.current_multiplier;
    }

    return res.status(200).json(responseData);

  } catch (error) {
    console.error('Move API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
