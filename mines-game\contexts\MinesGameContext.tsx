import React, { createContext, useContext, ReactNode } from 'react';
import { MinesGameState, MinesGameContextType } from '@/types';
import { useUniversalGame } from './UniversalGameContext';
import { useAuth } from './AuthContext';

const MinesGameContext = createContext<MinesGameContextType | undefined>(undefined);

interface MinesGameProviderProps {
  children: ReactNode;
}

export function MinesGameProvider({ children }: MinesGameProviderProps) {
  const universalGame = useUniversalGame();
  const { user } = useAuth();

  // Ensure we're working with a mines game
  const minesGame = universalGame.currentGameType === 'mines'
    ? universalGame.currentGame as MinesGameState | null
    : null;

  const minesHistory = universalGame.gameHistory.filter(
    game => game.game_type === 'mines'
  ) as MinesGameState[];

  /**
   * Start a new mines game
   */
  const startGame = async (betAmount: number, mineCount: number): Promise<boolean> => {
    return universalGame.startGame('mines', {
      bet_amount: betAmount,
      mine_count: mineCount
    });
  };

  /**
   * Reveal a cell in the mines game
   */
  const revealCell = async (cellIndex: number): Promise<{ hit: boolean; multiplier: number; gameOver: boolean; profit?: number }> => {
    try {
      console.log('🎯 MinesGameContext: Attempting to reveal cell', cellIndex);
      console.log('🎯 MinesGameContext: Current minesGame state:', minesGame);
      console.log('🎯 MinesGameContext: Universal game active:', universalGame.isGameActive());

      const result = await universalGame.makeMove({ cellIndex });
      console.log('🎯 MinesGameContext: Move result:', result);

      return {
        hit: result.hit || false,
        multiplier: result.multiplier || (minesGame?.current_multiplier || 1),
        gameOver: result.gameOver || false,
        profit: result.profit
      };
    } catch (error) {
      console.error('🎯 MinesGameContext: Reveal cell error:', error);
      throw error;
    }
  };

  /**
   * Cash out the current mines game
   */
  const cashOut = async (): Promise<{ success: boolean; profit: number }> => {
    return universalGame.cashOut();
  };

  /**
   * Reset the game state
   */
  const resetGame = () => {
    universalGame.resetGame();
  };

  /**
   * Switch to mines game mode
   */
  const switchToMines = () => {
    universalGame.switchGame('mines');
  };

  /**
   * Load mines game history
   */
  const loadGameHistory = async () => {
    await universalGame.loadGameHistory('mines');
  };

  /**
   * Check if player can cash out
   */
  const canCashOut = (): boolean => {
    return minesGame?.status === 'active' &&
           (minesGame?.revealed_cells?.length || 0) > 0;
  };

  /**
   * Get safe cells remaining
   */
  const getSafeCellsRemaining = (): number => {
    if (!minesGame) return 0;
    const totalSafeCells = (minesGame.grid_size || 25) - minesGame.mine_count;
    return totalSafeCells - minesGame.revealed_cells.length;
  };

  /**
   * Get next multiplier if a safe cell is revealed
   */
  const getNextMultiplier = (): number => {
    if (!minesGame || minesGame.status !== 'active') {
      return minesGame?.current_multiplier || 1.0;
    }

    // This would need to be calculated by the game provider
    // For now, return current multiplier
    return minesGame.current_multiplier;
  };

  /**
   * Check if a cell is revealed
   */
  const isCellRevealed = (cellIndex: number): boolean => {
    return minesGame?.revealed_cells?.includes(cellIndex) || false;
  };

  /**
   * Check if a cell is a mine (only visible when game is over)
   */
  const isCellMine = (cellIndex: number): boolean => {
    if (!minesGame || minesGame.status === 'active') return false;
    return minesGame.mine_positions?.includes(cellIndex) || false;
  };

  /**
   * Get game statistics
   */
  const getGameStats = () => {
    if (!minesGame) return null;

    return {
      gridSize: minesGame.grid_size || 25,
      mineCount: minesGame.mine_count,
      revealedCells: minesGame.revealed_cells.length,
      safeCellsRemaining: getSafeCellsRemaining(),
      currentMultiplier: minesGame.current_multiplier,
      profit: minesGame.profit,
      status: minesGame.status
    };
  };



  const value: MinesGameContextType = {
    gameState: minesGame,
    gameHistory: minesHistory,
    loading: universalGame.loading,
    error: universalGame.error,
    startGame,
    revealCell,
    cashOut,
    resetGame,
    makeMove: revealCell, // Alias for consistency

    // Mines-specific methods
    switchToMines,
    loadGameHistory,
    canCashOut,
    getSafeCellsRemaining,
    getNextMultiplier,
    isCellRevealed,
    isCellMine,
    getGameStats
  };

  return (
    <MinesGameContext.Provider value={value}>
      {children}
    </MinesGameContext.Provider>
  );
}

export function useMinesGame() {
  const context = useContext(MinesGameContext);
  if (context === undefined) {
    throw new Error('useMinesGame must be used within a MinesGameProvider');
  }
  return context;
}

// For backward compatibility, also export as useGame
export const useGame = useMinesGame;
