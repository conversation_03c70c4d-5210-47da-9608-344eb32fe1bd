import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { userDb, transactionDb, initDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { currency, amount, address } = req.body;

    // Validate input
    if (!currency || !['USDT', 'LTC'].includes(currency)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid currency. Must be USDT or LTC'
      });
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount'
      });
    }

    if (!address || typeof address !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Withdrawal address is required'
      });
    }

    // Validate amount limits
    const minWithdraw = currency === 'USDT' ? 5 : 0.01;
    const maxWithdraw = currency === 'USDT' ? 5000 : 5;

    if (amount < minWithdraw) {
      return res.status(400).json({
        success: false,
        error: `Minimum withdrawal is ${minWithdraw} ${currency}`
      });
    }

    if (amount > maxWithdraw) {
      return res.status(400).json({
        success: false,
        error: `Maximum withdrawal is ${maxWithdraw} ${currency}`
      });
    }

    // Check user balance
    const currentBalance = currency === 'USDT' ? user.usdt_balance : user.ltc_balance;
    
    if (amount > currentBalance) {
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance'
      });
    }

    // Deduct from user balance
    const newBalance = currentBalance - amount;
    userDb.updateBalance(user.id, currency, newBalance);

    // Create transaction record
    const transaction = transactionDb.create({
      user_id: user.id,
      type: 'withdraw',
      currency,
      amount,
      status: 'pending'
    });

    // In a real application, you would integrate with actual blockchain networks
    // For this demo, we'll simulate pending withdrawals
    
    // Get updated user data
    const updatedUser = userDb.findById(user.id);

    return res.status(200).json({
      success: true,
      transaction,
      balance: {
        usdt: updatedUser?.usdt_balance || 0,
        ltc: updatedUser?.ltc_balance || 0
      },
      message: `Withdrawal of ${amount} ${currency} initiated. It will be processed within 24 hours.`
    });
  } catch (error) {
    console.error('Withdraw API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
