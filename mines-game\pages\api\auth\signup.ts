import { NextApiRequest, NextApiResponse } from 'next';
import { registerUser, setAuthCookie, checkRateLimit, getClientIP } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Rate limiting
    const clientIP = getClientIP(req);
    if (!checkRateLimit(clientIP, 5, 15 * 60 * 1000)) {
      return res.status(429).json({
        success: false,
        error: 'Too many signup attempts. Please try again later.'
      });
    }

    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username, email, and password are required'
      });
    }

    const result = await registerUser(username, email, password);

    if (result.success && result.user) {
      // Generate token and set cookie
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { userId: result.user.id, username: result.user.username, email: result.user.email },
        process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
        { expiresIn: '7d' }
      );

      setAuthCookie(res, token);

      return res.status(201).json({
        success: true,
        user: result.user,
        message: 'Account created successfully'
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.error || 'Failed to create account'
      });
    }
  } catch (error) {
    console.error('Signup API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
