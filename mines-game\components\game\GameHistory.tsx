import React, { useState, useEffect } from 'react';
import { GameState, DiceGameState, MinesGameState } from '@/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { History, TrendingUp, TrendingDown, Gem, Bomb, ExternalLink, Dice1, Target } from 'lucide-react';
import { formatCurrency, formatDate, API_ENDPOINTS } from '@/lib/utils';

interface GameHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GameHistory({ isOpen, onClose }: GameHistoryProps) {
  const [games, setGames] = useState<GameState[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedGame, setSelectedGame] = useState<GameState | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadGameHistory();
    }
  }, [isOpen]);

  const loadGameHistory = async () => {
    setLoading(true);
    try {
      const response = await fetch(API_ENDPOINTS.GAME.HISTORY, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setGames(data.games);
        }
      }
    } catch (error) {
      console.error('Failed to load game history:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'won':
        return 'text-green-400';
      case 'lost':
        return 'text-red-400';
      case 'cashed_out':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'won':
        return <TrendingUp className="h-4 w-4" />;
      case 'lost':
        return <TrendingDown className="h-4 w-4" />;
      case 'cashed_out':
        return <Gem className="h-4 w-4" />;
      default:
        return <History className="h-4 w-4" />;
    }
  };

  const getGameTypeIcon = (gameType: string) => {
    switch (gameType) {
      case 'mines':
        return <Bomb className="h-4 w-4" />;
      case 'dice':
        return <Dice1 className="h-4 w-4" />;
      default:
        return <Target className="h-4 w-4" />;
    }
  };

  const getGameTypeName = (gameType: string) => {
    switch (gameType) {
      case 'mines':
        return 'Mines';
      case 'dice':
        return 'Dice';
      default:
        return gameType.charAt(0).toUpperCase() + gameType.slice(1);
    }
  };

  const renderGameSpecificDetails = (game: GameState) => {
    if (game.game_type === 'mines') {
      const minesGame = game as MinesGameState;
      return (
        <>
          <div className="text-sm text-gray-300">
            <span>Mines: {minesGame.mine_count || 'N/A'}</span>
          </div>
          <div className="text-sm text-gray-300">
            <span>Revealed: {(minesGame.revealed_cells || []).length}</span>
          </div>
        </>
      );
    } else if (game.game_type === 'dice') {
      const diceGame = game as DiceGameState;

      return (
        <>
          <div className="text-sm text-gray-300">
            <span>Target: {diceGame.roll_under ? '<' : '>'} {diceGame.target_number}</span>
          </div>
          <div className="text-sm text-gray-300">
            <span>Result: {diceGame.result !== undefined ? diceGame.result : 'N/A'}</span>
          </div>
        </>
      );
    }

    // Default for unknown game types
    return (
      <div className="text-sm text-gray-300">
        <span>Multiplier: {game.current_multiplier.toFixed(2)}x</span>
      </div>
    );
  };

  const renderGameGrid = (game: GameState) => {
    if (game.game_type === 'mines') {
      // Handle both old and new game state formats
      const revealedCells = (game as any).revealed_cells || [];
      const minePositions = (game as any).mine_positions || [];
      const gridSize = (game as any).grid_size || 25;

      const cells = Array.from({ length: gridSize }, (_, index) => {
        const isRevealed = revealedCells.includes(index);
        const isMine = minePositions.includes(index);

        if (isRevealed) {
          return (
            <div
              key={index}
              className={`w-4 h-4 rounded-sm flex items-center justify-center ${
                isMine ? 'bg-red-500' : 'bg-green-500'
              }`}
            >
              {isMine ? (
                <Bomb className="h-2 w-2 text-white" />
              ) : (
                <Gem className="h-2 w-2 text-white" />
              )}
            </div>
          );
        }

        // Show unrevealed mines for completed games
        if (game.status !== 'active' && isMine) {
          return (
            <div
              key={index}
              className="w-4 h-4 rounded-sm bg-red-900/50 border border-red-700 flex items-center justify-center"
            >
              <Bomb className="h-2 w-2 text-red-400" />
            </div>
          );
        }

        return (
          <div
            key={index}
            className="w-4 h-4 rounded-sm bg-gray-600 border border-gray-500"
          />
        );
      });

      return (
        <div className="grid grid-cols-5 gap-1 p-2 bg-gray-900/50 rounded">
          {cells}
        </div>
      );
    } else if (game.game_type === 'dice') {
      const diceGame = game as DiceGameState;
      return (
        <div className="p-4 bg-gray-900/50 rounded text-center">
          <div className="flex items-center justify-center space-x-4">
            <Dice1 className="h-8 w-8 text-blue-400" />
            <div className="text-lg font-bold">
              {diceGame.result !== undefined ? diceGame.result : '?'}
            </div>
          </div>
          <div className="text-sm text-gray-300 mt-2">
            Target: {diceGame.roll_under ? '<' : '>'} {diceGame.target_number}
          </div>
        </div>
      );
    }

    // Default for unknown game types
    return (
      <div className="p-4 bg-gray-900/50 rounded text-center">
        <div className="text-lg font-bold">{getGameTypeName(game.game_type)}</div>
        <div className="text-sm text-gray-300">Multiplier: {game.current_multiplier.toFixed(2)}x</div>
      </div>
    );
  };

  const totalProfit = games.reduce((sum, game) => sum + game.profit, 0);
  const totalGames = games.length;
  const winRate = totalGames > 0 ? (games.filter(g => g.profit > 0).length / totalGames) * 100 : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl bg-gray-800 border-gray-700 text-white max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <History className="h-5 w-5 mr-2" />
            Game History
          </DialogTitle>
        </DialogHeader>

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card className="bg-gray-700/50 border-gray-600">
            <CardContent className="pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{totalGames}</div>
                <div className="text-sm text-gray-300">Total Games</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-700/50 border-gray-600">
            <CardContent className="pt-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {totalProfit >= 0 ? '+' : ''}{formatCurrency(totalProfit)}
                </div>
                <div className="text-sm text-gray-300">Total Profit</div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-700/50 border-gray-600">
            <CardContent className="pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{winRate.toFixed(1)}%</div>
                <div className="text-sm text-gray-300">Win Rate</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Game List */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
            <div className="text-gray-300 mt-2">Loading history...</div>
          </div>
        ) : games.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            No games played yet. Start your first game!
          </div>
        ) : (
          <div className="space-y-3">
            {games.map((game) => (
              <Card
                key={game.id}
                className="bg-gray-700/30 border-gray-600 hover:bg-gray-700/50 transition-colors cursor-pointer"
                onClick={() => setSelectedGame(game)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`flex items-center space-x-2 ${getStatusColor(game.status)}`}>
                        {getStatusIcon(game.status)}
                        <span className="font-medium capitalize">{game.status.replace('_', ' ')}</span>
                      </div>

                      <div className="flex items-center space-x-2 text-sm text-gray-300">
                        {getGameTypeIcon(game.game_type)}
                        <span>{getGameTypeName(game.game_type)}</span>
                      </div>

                      <div className="text-sm text-gray-300">
                        {formatDate(game.created_at || '')}
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-sm text-gray-300">
                        <span>Bet: {formatCurrency(game.bet_amount)} USDT</span>
                      </div>

                      {renderGameSpecificDetails(game)}

                      <div className={`font-semibold ${game.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {game.profit >= 0 ? '+' : ''}{formatCurrency(game.profit)} USDT
                      </div>

                      <Button variant="ghost" size="sm">
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Game Detail Modal */}
        {selectedGame && (
          <Dialog open={!!selectedGame} onOpenChange={() => setSelectedGame(null)}>
            <DialogContent className="sm:max-w-md bg-gray-800 border-gray-700 text-white">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  {getGameTypeIcon(selectedGame.game_type)}
                  <span>{getGameTypeName(selectedGame.game_type)} Game Details</span>
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-4">
                <div className="text-center">
                  {renderGameGrid(selectedGame)}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-300">Game Type:</span>
                    <span className="ml-2 font-semibold">{getGameTypeName(selectedGame.game_type)}</span>
                  </div>

                  <div>
                    <span className="text-gray-300">Status:</span>
                    <span className={`ml-2 font-semibold ${getStatusColor(selectedGame.status)}`}>
                      {selectedGame.status.replace('_', ' ')}
                    </span>
                  </div>

                  <div>
                    <span className="text-gray-300">Bet Amount:</span>
                    <span className="ml-2 font-semibold">{formatCurrency(selectedGame.bet_amount)} USDT</span>
                  </div>

                  <div>
                    <span className="text-gray-300">Multiplier:</span>
                    <span className="ml-2 font-semibold">{selectedGame.current_multiplier.toFixed(2)}x</span>
                  </div>

                  {/* Game-specific details */}
                  {selectedGame.game_type === 'mines' && (
                    <>
                      <div>
                        <span className="text-gray-300">Mines:</span>
                        <span className="ml-2 font-semibold">{(selectedGame as MinesGameState).mine_count || 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-300">Revealed:</span>
                        <span className="ml-2 font-semibold">{((selectedGame as MinesGameState).revealed_cells || []).length}</span>
                      </div>
                    </>
                  )}

                  {selectedGame.game_type === 'dice' && (
                    <>
                      <div>
                        <span className="text-gray-300">Target:</span>
                        <span className="ml-2 font-semibold">
                          {(selectedGame as DiceGameState).roll_under ? '<' : '>'} {(selectedGame as DiceGameState).target_number}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-300">Result:</span>
                        <span className="ml-2 font-semibold">
                          {(selectedGame as DiceGameState).result !== undefined ? (selectedGame as DiceGameState).result : 'N/A'}
                        </span>
                      </div>
                    </>
                  )}

                  <div>
                    <span className="text-gray-300">Profit:</span>
                    <span className={`ml-2 font-semibold ${selectedGame.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {selectedGame.profit >= 0 ? '+' : ''}{formatCurrency(selectedGame.profit)} USDT
                    </span>
                  </div>
                </div>

                <div className="text-xs text-gray-400">
                  <div>Game ID: {selectedGame.id}</div>
                  <div>Client Seed: {selectedGame.client_seed}</div>
                  <div>Date: {formatDate(selectedGame.created_at || '')}</div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}
