import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    console.log('📊 Reset Stats API - Resetting session for user:', user.id);

    // Instead of deleting games, we just return success
    // The actual session reset happens on the client side via SessionStorage.resetSession()
    // This API endpoint exists for consistency but doesn't need to do anything with the database

    console.log('📊 Session reset completed for user:', user.id);

    res.status(200).json({
      success: true,
      message: 'Session stats reset successfully',
      resetTime: new Date().toISOString()
    });
  } catch (error) {
    console.error('Reset stats API error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});
