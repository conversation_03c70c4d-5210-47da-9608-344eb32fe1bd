import { GameState, User, MinesGameState } from '@/types';
import { generateServerSeed, generateClientSeed, generateMinePositions, hashServerSeed } from './crypto';
import { gameDb, userDb, transactionDb } from './database';
import { GAME_CONFIG } from './utils';

/**
 * Calculate multiplier based on mines and revealed safe cells
 * Includes house edge to ensure profitability
 */
export function calculateMultiplier(mineCount: number, revealedSafeCells: number, gridSize: number = 25): number {
  if (revealedSafeCells === 0) return 1.0;

  const totalSafeCells = gridSize - mineCount;
  const remainingSafeCells = totalSafeCells - revealedSafeCells;

  if (remainingSafeCells <= 0) return 1.0;

  // Base multiplier calculation: probability of next cell being safe
  const baseProbability = remainingSafeCells / (gridSize - mineCount - revealedSafeCells + 1);
  const baseMultiplier = 1 / baseProbability;

  // Apply house edge (4% default)
  const houseEdgeMultiplier = 1 - GAME_CONFIG.HOUSE_EDGE;

  // Progressive multiplier that increases with each safe reveal
  const progressiveBonus = 1 + (revealedSafeCells * 0.02); // 2% bonus per safe cell

  const finalMultiplier = baseMultiplier * houseEdgeMultiplier * progressiveBonus;

  return Math.max(1.01, Number(finalMultiplier.toFixed(4)));
}

/**
 * Calculate potential profit for current game state
 */
export function calculateProfit(betAmount: number, multiplier: number): number {
  return Number((betAmount * multiplier - betAmount).toFixed(8));
}

/**
 * Validate game parameters
 */
export function validateGameParams(betAmount: number, mineCount: number, userBalance: number): string | null {
  if (betAmount < GAME_CONFIG.MIN_BET) {
    return `Minimum bet is ${GAME_CONFIG.MIN_BET} USDT`;
  }

  if (betAmount > GAME_CONFIG.MAX_BET) {
    return `Maximum bet is ${GAME_CONFIG.MAX_BET} USDT`;
  }

  if (betAmount > userBalance) {
    return 'Insufficient balance';
  }

  if (mineCount < GAME_CONFIG.MIN_MINES) {
    return `Minimum ${GAME_CONFIG.MIN_MINES} mine required`;
  }

  if (mineCount > GAME_CONFIG.MAX_MINES) {
    return `Maximum ${GAME_CONFIG.MAX_MINES} mines allowed`;
  }

  return null;
}

/**
 * Start a new game
 */
export async function startGame(
  userId: number,
  betAmount: number,
  mineCount: number,
  clientSeed?: string
): Promise<{ success: boolean; game?: GameState; error?: string }> {
  try {
    // Get user and validate balance
    const user = userDb.findById(userId);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // Check if user has an active game
    const activeGame = gameDb.findActiveByUserId(userId);
    if (activeGame) {
      return { success: false, error: 'You have an active game. Please finish it first.' };
    }

    // Validate game parameters
    const validationError = validateGameParams(betAmount, mineCount, user.usdt_balance);
    if (validationError) {
      return { success: false, error: validationError };
    }

    // Generate seeds for provably fair gaming
    const serverSeed = generateServerSeed();
    const gameClientSeed = clientSeed || generateClientSeed();

    // Generate mine positions
    const minePositions = generateMinePositions(serverSeed, gameClientSeed, mineCount);

    // Deduct bet amount from user balance
    const newBalance = user.usdt_balance - betAmount;
    userDb.updateBalance(userId, 'USDT', newBalance);

    // Create game record
    const gameData: Omit<MinesGameState, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      game_type: 'mines',
      grid_size: GAME_CONFIG.GRID_SIZE,
      mine_count: mineCount,
      bet_amount: betAmount,
      current_multiplier: 1.0,
      status: 'active',
      revealed_cells: [],
      mine_positions: minePositions,
      server_seed: serverSeed,
      client_seed: gameClientSeed,
      profit: 0
    };

    const game = gameDb.create(gameData);

    // Create bet transaction
    transactionDb.create({
      user_id: userId,
      game_id: game.id,
      type: 'bet',
      currency: 'USDT',
      amount: betAmount,
      status: 'completed'
    });

    // Don't expose mine positions or server seed to client
    const clientGame = {
      ...game,
      mine_positions: [], // Hide mine positions
      server_seed: hashServerSeed(serverSeed) // Only send hash
    };

    return { success: true, game: clientGame };
  } catch (error) {
    console.error('Start game error:', error);
    return { success: false, error: 'Failed to start game' };
  }
}

/**
 * Reveal a cell in the game
 */
export async function revealCell(
  gameId: number,
  cellIndex: number,
  userId: number
): Promise<{ success: boolean; hit?: boolean; multiplier?: number; gameOver?: boolean; profit?: number; minePositions?: number[]; error?: string }> {
  try {
    // Get game
    const game = gameDb.findById(gameId);
    if (!game) {
      return { success: false, error: 'Game not found' };
    }

    // Verify game belongs to user
    if (game.user_id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Check if game is active
    if (game.status !== 'active') {
      return { success: false, error: 'Game is not active' };
    }

    // Cast to MinesGameState for type safety
    const minesGame = game as MinesGameState;

    // Validate cell index
    if (cellIndex < 0 || cellIndex >= minesGame.grid_size) {
      return { success: false, error: 'Invalid cell index' };
    }

    // Check if cell already revealed
    if (minesGame.revealed_cells.includes(cellIndex)) {
      return { success: false, error: 'Cell already revealed' };
    }

    // Check if cell is a mine
    const isMine = minesGame.mine_positions.includes(cellIndex);

    if (isMine) {
      // Game over - hit mine
      gameDb.update(gameId, {
        status: 'lost',
        revealed_cells: [...minesGame.revealed_cells, cellIndex],
        profit: -minesGame.bet_amount
      });

      return {
        success: true,
        hit: true,
        multiplier: 0,
        gameOver: true,
        profit: -minesGame.bet_amount,
        minePositions: minesGame.mine_positions // Return mine positions when game is over
      };
    } else {
      // Safe cell - update game state
      const newRevealedCells = [...minesGame.revealed_cells, cellIndex];
      const newMultiplier = calculateMultiplier(minesGame.mine_count, newRevealedCells.length, minesGame.grid_size);
      const currentProfit = calculateProfit(minesGame.bet_amount, newMultiplier);

      gameDb.update(gameId, {
        revealed_cells: newRevealedCells,
        current_multiplier: newMultiplier,
        profit: currentProfit
      });

      // Check if all safe cells are revealed (auto-win)
      const totalSafeCells = minesGame.grid_size - minesGame.mine_count;
      const gameOver = newRevealedCells.length === totalSafeCells;

      if (gameOver) {
        // Auto cash out
        const finalProfit = currentProfit;
        const totalPayout = minesGame.bet_amount + finalProfit;

        // Update game status
        gameDb.update(gameId, {
          status: 'won',
          profit: finalProfit
        });

        // Add winnings to user balance
        const user = userDb.findById(userId)!;
        userDb.updateBalance(userId, 'USDT', user.usdt_balance + totalPayout);

        // Create win transaction
        transactionDb.create({
          user_id: userId,
          game_id: gameId,
          type: 'win',
          currency: 'USDT',
          amount: totalPayout,
          status: 'completed'
        });

        return {
          success: true,
          hit: false,
          multiplier: newMultiplier,
          gameOver: true,
          profit: finalProfit,
          minePositions: minesGame.mine_positions // Return mine positions when game is over
        };
      }

      return {
        success: true,
        hit: false,
        multiplier: newMultiplier,
        gameOver: false,
        profit: currentProfit
      };
    }
  } catch (error) {
    console.error('Reveal cell error:', error);
    return { success: false, error: 'Failed to reveal cell' };
  }
}

/**
 * Cash out current game
 */
export async function cashOut(
  gameId: number,
  userId: number
): Promise<{ success: boolean; profit?: number; error?: string }> {
  try {
    // Get game
    const game = gameDb.findById(gameId);
    if (!game) {
      return { success: false, error: 'Game not found' };
    }

    // Verify game belongs to user
    if (game.user_id !== userId) {
      return { success: false, error: 'Unauthorized' };
    }

    // Check if game is active
    if (game.status !== 'active') {
      return { success: false, error: 'Game is not active' };
    }

    // Cast to MinesGameState for type safety
    const minesGame = game as MinesGameState;

    // Check if any cells have been revealed
    if (minesGame.revealed_cells.length === 0) {
      return { success: false, error: 'No cells revealed yet' };
    }

    // Calculate final profit and payout
    const finalProfit = game.profit;
    const totalPayout = game.bet_amount + finalProfit;

    // Update game status
    gameDb.update(gameId, {
      status: 'cashed_out'
    });

    // Add winnings to user balance
    const user = userDb.findById(userId)!;
    userDb.updateBalance(userId, 'USDT', user.usdt_balance + totalPayout);

    // Create win transaction
    transactionDb.create({
      user_id: userId,
      game_id: gameId,
      type: 'win',
      currency: 'USDT',
      amount: totalPayout,
      status: 'completed'
    });

    return { success: true, profit: finalProfit };
  } catch (error) {
    console.error('Cash out error:', error);
    return { success: false, error: 'Failed to cash out' };
  }
}

/**
 * Get game history for user
 */
export function getGameHistory(userId: number, limit: number = 50): GameState[] {
  return gameDb.findByUserId(userId, limit);
}

/**
 * Get current active game for user
 */
export function getActiveGame(userId: number): GameState | null {
  return gameDb.findActiveByUserId(userId);
}
