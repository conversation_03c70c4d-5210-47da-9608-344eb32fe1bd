import Database from 'better-sqlite3';
import path from 'path';
import { User, GameState, Transaction } from '@/types';

// Database instance
let db: Database.Database | null = null;

/**
 * Initialize database connection and create tables
 */
export function initDatabase(): Database.Database {
  if (db) return db;

  const dbPath = path.join(process.cwd(), 'data', 'mines.db');

  try {
    // Ensure data directory exists
    const fs = require('fs');
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new Database(dbPath);

    // Enable WAL mode for better performance
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 1000000');
    db.pragma('temp_store = memory');

    createTables();

    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Create database tables
 */
function createTables(): void {
  if (!db) throw new Error('Database not initialized');

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      usdt_balance REAL DEFAULT 0.0,
      ltc_balance REAL DEFAULT 0.0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Check if games table exists and what schema it has
  const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='games'").get();

  if (tableExists) {
    // Check if we need to migrate old games table
    const tableInfo = db.prepare("PRAGMA table_info(games)").all();
    const hasOldColumns = tableInfo.some((col: any) =>
      ['grid_size', 'mine_count', 'revealed_cells', 'mine_positions'].includes(col.name)
    );
    const hasGameType = tableInfo.some((col: any) => col.name === 'game_type');
    const hasGameData = tableInfo.some((col: any) => col.name === 'game_data');

    if (hasOldColumns || !hasGameType || !hasGameData) {
      console.log('🔄 Migrating games table to support multiple game types...');
      migrateGamesTable();
    }
  } else {
    // Create new table with correct schema
    console.log('📋 Creating new games table with multi-game support...');
    db.exec(`
      CREATE TABLE games (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        game_type TEXT NOT NULL DEFAULT 'mines',
        bet_amount REAL NOT NULL,
        current_multiplier REAL DEFAULT 1.0,
        status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',
        server_seed TEXT NOT NULL,
        client_seed TEXT NOT NULL,
        server_seed_hash TEXT DEFAULT '',
        profit REAL DEFAULT 0.0,
        game_data TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `);
  }

  // Transactions table
  db.exec(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      game_id INTEGER,
      type TEXT CHECK(type IN ('deposit', 'withdraw', 'bet', 'win')) NOT NULL,
      currency TEXT CHECK(currency IN ('USDT', 'LTC')) NOT NULL,
      amount REAL NOT NULL,
      status TEXT CHECK(status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
      transaction_hash TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (game_id) REFERENCES games (id)
    )
  `);

  // Phase 2: Enhanced tables for leaderboards, statistics, and sessions

  // User Statistics table - tracks detailed user performance
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_statistics (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      game_type TEXT NOT NULL,
      total_games INTEGER DEFAULT 0,
      total_wins INTEGER DEFAULT 0,
      total_losses INTEGER DEFAULT 0,
      total_wagered REAL DEFAULT 0.0,
      total_profit REAL DEFAULT 0.0,
      biggest_win REAL DEFAULT 0.0,
      biggest_loss REAL DEFAULT 0.0,
      highest_multiplier REAL DEFAULT 0.0,
      current_streak INTEGER DEFAULT 0,
      best_win_streak INTEGER DEFAULT 0,
      best_loss_streak INTEGER DEFAULT 0,
      last_played DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, game_type)
    )
  `);

  // Leaderboards table - tracks top performers
  db.exec(`
    CREATE TABLE IF NOT EXISTS leaderboards (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      username TEXT NOT NULL,
      game_type TEXT NOT NULL,
      category TEXT NOT NULL, -- 'profit', 'multiplier', 'streak', 'volume'
      value REAL NOT NULL,
      rank_position INTEGER,
      period TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'
      period_start DATETIME NOT NULL,
      period_end DATETIME NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id),
      UNIQUE(user_id, game_type, category, period, period_start)
    )
  `);

  // Game Sessions table - tracks user gaming sessions
  db.exec(`
    CREATE TABLE IF NOT EXISTS game_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      session_start DATETIME DEFAULT CURRENT_TIMESTAMP,
      session_end DATETIME,
      total_games INTEGER DEFAULT 0,
      total_wagered REAL DEFAULT 0.0,
      total_profit REAL DEFAULT 0.0,
      games_won INTEGER DEFAULT 0,
      games_lost INTEGER DEFAULT 0,
      biggest_win REAL DEFAULT 0.0,
      biggest_loss REAL DEFAULT 0.0,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);

  // Achievements table - tracks user achievements and badges
  db.exec(`
    CREATE TABLE IF NOT EXISTS achievements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      code TEXT UNIQUE NOT NULL,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      icon TEXT NOT NULL,
      category TEXT NOT NULL, -- 'wins', 'profit', 'streak', 'volume', 'special'
      requirement_type TEXT NOT NULL, -- 'count', 'value', 'streak'
      requirement_value REAL NOT NULL,
      reward_type TEXT, -- 'badge', 'bonus', 'title'
      reward_value REAL DEFAULT 0.0,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // User Achievements table - tracks which achievements users have earned
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_achievements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      achievement_id INTEGER NOT NULL,
      earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      progress REAL DEFAULT 0.0,
      is_completed BOOLEAN DEFAULT 0,
      FOREIGN KEY (user_id) REFERENCES users (id),
      FOREIGN KEY (achievement_id) REFERENCES achievements (id),
      UNIQUE(user_id, achievement_id)
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);
    CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
    CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);
    CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);
    CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);
    CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
    CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);

    -- Phase 2 indexes
    CREATE INDEX IF NOT EXISTS idx_user_statistics_user_game ON user_statistics(user_id, game_type);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_game_category ON leaderboards(game_type, category);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_period ON leaderboards(period, period_start);
    CREATE INDEX IF NOT EXISTS idx_leaderboards_rank ON leaderboards(rank_position);
    CREATE INDEX IF NOT EXISTS idx_game_sessions_user_active ON game_sessions(user_id, is_active);
    CREATE INDEX IF NOT EXISTS idx_game_sessions_start ON game_sessions(session_start);
    CREATE INDEX IF NOT EXISTS idx_achievements_category ON achievements(category);
    CREATE INDEX IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_achievements_completed ON user_achievements(is_completed);
  `);

  // Create triggers for updated_at
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_users_timestamp
    AFTER UPDATE ON users
    BEGIN
      UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_games_timestamp
    AFTER UPDATE ON games
    BEGIN
      UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // Phase 2 triggers
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_user_statistics_timestamp
    AFTER UPDATE ON user_statistics
    BEGIN
      UPDATE user_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_leaderboards_timestamp
    AFTER UPDATE ON leaderboards
    BEGIN
      UPDATE leaderboards SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_game_sessions_timestamp
    AFTER UPDATE ON game_sessions
    BEGIN
      UPDATE game_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);
}

/**
 * Migrate old games table to support multiple game types
 */
function migrateGamesTable(): void {
  if (!db) throw new Error('Database not initialized');

  try {
    // Check if old columns exist
    const tableInfo = db.prepare("PRAGMA table_info(games)").all();
    const hasOldColumns = tableInfo.some((col: any) =>
      ['grid_size', 'mine_count', 'revealed_cells', 'mine_positions'].includes(col.name)
    );

    if (hasOldColumns) {
      console.log('📦 Migrating existing mines games...');

      // Disable foreign key constraints during migration
      db.exec('PRAGMA foreign_keys = OFF');

      // First, backup existing data
      const existingGames = db.prepare(`
        SELECT * FROM games
      `).all();

      console.log(`📋 Found ${existingGames.length} existing games to migrate`);

      // Create new table with correct schema (without foreign key for now)
      db.exec(`
        CREATE TABLE games_new (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          game_type TEXT NOT NULL DEFAULT 'mines',
          bet_amount REAL NOT NULL,
          current_multiplier REAL DEFAULT 1.0,
          status TEXT CHECK(status IN ('active', 'won', 'lost', 'cashed_out', 'cancelled')) DEFAULT 'active',
          server_seed TEXT NOT NULL,
          client_seed TEXT NOT NULL,
          server_seed_hash TEXT DEFAULT '',
          profit REAL DEFAULT 0.0,
          game_data TEXT DEFAULT '{}',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Migrate data to new table
      for (const game of existingGames) {
        const gameData = {
          grid_size: game.grid_size || 25,
          mine_count: game.mine_count,
          revealed_cells: JSON.parse(game.revealed_cells || '[]'),
          mine_positions: JSON.parse(game.mine_positions || '[]')
        };

        db.prepare(`
          INSERT INTO games_new (
            id, user_id, game_type, bet_amount, current_multiplier,
            status, server_seed, client_seed, server_seed_hash, profit,
            game_data, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          game.id,
          game.user_id,
          'mines',
          game.bet_amount,
          game.current_multiplier,
          game.status,
          game.server_seed,
          game.client_seed,
          game.server_seed_hash || '',
          game.profit,
          JSON.stringify(gameData),
          game.created_at,
          game.updated_at
        );
      }

      // Drop old table and rename new one
      db.exec(`DROP TABLE games`);
      db.exec(`ALTER TABLE games_new RENAME TO games`);

      // Recreate indexes
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_games_user_id ON games(user_id);
        CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
        CREATE INDEX IF NOT EXISTS idx_games_type ON games(game_type);
        CREATE INDEX IF NOT EXISTS idx_games_user_type ON games(user_id, game_type);
        CREATE INDEX IF NOT EXISTS idx_games_user_status ON games(user_id, status);
      `);

      // Recreate trigger
      db.exec(`
        CREATE TRIGGER IF NOT EXISTS update_games_timestamp
        AFTER UPDATE ON games
        BEGIN
          UPDATE games SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `);

      // Re-enable foreign key constraints
      db.exec('PRAGMA foreign_keys = ON');

      console.log(`✅ Migrated ${existingGames.length} mines games to new schema`);
    }

    console.log('✅ Games table migration completed');
  } catch (error) {
    console.error('❌ Games table migration failed:', error);
    throw error;
  }
}

/**
 * Get database instance
 */
export function getDatabase(): Database.Database {
  if (!db) {
    return initDatabase();
  }
  return db;
}

/**
 * Close database connection
 */
export function closeDatabase(): void {
  if (db) {
    db.close();
    db = null;
  }
}

/**
 * User database operations
 */
export const userDb = {
  create: (username: string, email: string, passwordHash: string): User => {
    const db = getDatabase();
    const stmt = db.prepare(`
      INSERT INTO users (username, email, password_hash)
      VALUES (?, ?, ?)
    `);

    const result = stmt.run(username, email, passwordHash);

    return userDb.findById(result.lastInsertRowid as number)!;
  },

  findById: (id: number): User | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM users WHERE id = ?');
    return stmt.get(id) as User | null;
  },

  findByEmail: (email: string): User | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM users WHERE email = ?');
    return stmt.get(email) as User | null;
  },

  findByUsername: (username: string): User | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
    return stmt.get(username) as User | null;
  },

  updateBalance: (userId: number, currency: 'USDT' | 'LTC', amount: number): boolean => {
    const db = getDatabase();
    const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';
    const stmt = db.prepare(`UPDATE users SET ${column} = ? WHERE id = ?`);
    const result = stmt.run(amount, userId);
    return result.changes > 0;
  },

  addToBalance: (userId: number, currency: 'USDT' | 'LTC', amount: number): boolean => {
    const db = getDatabase();
    const column = currency === 'USDT' ? 'usdt_balance' : 'ltc_balance';
    const stmt = db.prepare(`UPDATE users SET ${column} = ${column} + ? WHERE id = ?`);
    const result = stmt.run(amount, userId);
    return result.changes > 0;
  }
};

/**
 * Game database operations
 */
export const gameDb = {
  create: (gameData: Omit<GameState, 'id' | 'created_at' | 'updated_at'>): GameState => {
    const db = getDatabase();

    // Extract game-specific data
    const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = gameData;

    const stmt = db.prepare(`
      INSERT INTO games (
        user_id, game_type, bet_amount, current_multiplier,
        status, server_seed, client_seed, server_seed_hash, profit, game_data
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      user_id,
      game_type,
      bet_amount,
      current_multiplier,
      status,
      server_seed,
      client_seed,
      '', // server_seed_hash will be set separately
      profit,
      JSON.stringify(specificData)
    );

    return gameDb.findById(result.lastInsertRowid as number)!;
  },

  findById: (id: number): GameState | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM games WHERE id = ?');
    const game = stmt.get(id) as any;

    if (!game) return null;

    return gameDb.parseGameData(game);
  },

  update: (id: number, updates: Partial<GameState>): boolean => {
    const db = getDatabase();

    // Separate base fields from game-specific data
    const { game_type, user_id, bet_amount, current_multiplier, status, server_seed, client_seed, profit, ...specificData } = updates;

    const baseFields: any = {};
    if (bet_amount !== undefined) baseFields.bet_amount = bet_amount;
    if (current_multiplier !== undefined) baseFields.current_multiplier = current_multiplier;
    if (status !== undefined) baseFields.status = status;
    if (profit !== undefined) baseFields.profit = profit;

    // If there's game-specific data, update game_data field
    if (Object.keys(specificData).length > 0) {
      // Get current game data and merge
      const currentGame = gameDb.findById(id);
      if (currentGame) {
        const currentSpecificData = gameDb.extractGameSpecificData(currentGame);
        const mergedData = { ...currentSpecificData, ...specificData };
        baseFields.game_data = JSON.stringify(mergedData);
      }
    }

    if (Object.keys(baseFields).length === 0) return false;

    const fields = Object.keys(baseFields);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => baseFields[field]);

    const stmt = db.prepare(`UPDATE games SET ${setClause} WHERE id = ?`);
    const result = stmt.run(...values, id);
    return result.changes > 0;
  },

  findActiveByUserId: (userId: number): GameState | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM games WHERE user_id = ? AND status = ? ORDER BY created_at DESC LIMIT 1');
    const game = stmt.get(userId, 'active') as any;

    if (!game) return null;

    return gameDb.parseGameData(game);
  },

  findByUserId: (userId: number, limit: number = 50, gameType?: string): GameState[] => {
    const db = getDatabase();
    let query = 'SELECT * FROM games WHERE user_id = ?';
    const params: any[] = [userId];

    if (gameType) {
      query += ' AND game_type = ?';
      params.push(gameType);
    }

    query += ' ORDER BY created_at DESC LIMIT ?';
    params.push(limit);

    const stmt = db.prepare(query);
    const games = stmt.all(...params) as any[];

    return games.map(game => gameDb.parseGameData(game));
  },

  // Helper method to parse game data from database
  parseGameData: (dbGame: any): GameState => {
    const gameData = JSON.parse(dbGame.game_data || '{}');

    return {
      ...dbGame,
      ...gameData
    } as GameState;
  },

  // Helper method to extract game-specific data
  extractGameSpecificData: (gameState: GameState): any => {
    const { id, user_id, game_type, bet_amount, current_multiplier, status, server_seed, client_seed, profit, created_at, updated_at, ...specificData } = gameState;
    return specificData;
  }
};

/**
 * Transaction database operations
 */
export const transactionDb = {
  create: (transactionData: Omit<Transaction, 'id' | 'created_at'>): Transaction => {
    const db = getDatabase();
    const stmt = db.prepare(`
      INSERT INTO transactions (user_id, game_id, type, currency, amount, status, transaction_hash)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      transactionData.user_id,
      transactionData.game_id || null,
      transactionData.type,
      transactionData.currency,
      transactionData.amount,
      transactionData.status,
      transactionData.transaction_hash || null
    );

    return transactionDb.findById(result.lastInsertRowid as number)!;
  },

  findById: (id: number): Transaction | null => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM transactions WHERE id = ?');
    return stmt.get(id) as Transaction | null;
  },

  findByUserId: (userId: number, limit: number = 50): Transaction[] => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ?');
    return stmt.all(userId, limit) as Transaction[];
  },

  updateStatus: (id: number, status: 'pending' | 'completed' | 'failed'): boolean => {
    const db = getDatabase();
    const stmt = db.prepare('UPDATE transactions SET status = ? WHERE id = ?');
    const result = stmt.run(status, id);
    return result.changes > 0;
  }
};

/**
 * Phase 2: User Statistics database operations
 */
export const userStatsDb = {
  create: (userId: number, gameType: string): any => {
    const db = getDatabase();
    const stmt = db.prepare(`
      INSERT INTO user_statistics (user_id, game_type)
      VALUES (?, ?)
    `);

    const result = stmt.run(userId, gameType);
    return userStatsDb.findByUserAndGame(userId, gameType);
  },

  findByUserAndGame: (userId: number, gameType: string): any => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ? AND game_type = ?');
    return stmt.get(userId, gameType);
  },

  findByUser: (userId: number): any[] => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM user_statistics WHERE user_id = ?');
    return stmt.all(userId);
  },

  updateStats: (userId: number, gameType: string, updates: any): boolean => {
    const db = getDatabase();
    const existing = userStatsDb.findByUserAndGame(userId, gameType);

    if (!existing) {
      userStatsDb.create(userId, gameType);
    }

    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);

    const stmt = db.prepare(`
      UPDATE user_statistics
      SET ${fields}, last_played = CURRENT_TIMESTAMP
      WHERE user_id = ? AND game_type = ?
    `);

    const result = stmt.run(...values, userId, gameType);
    return result.changes > 0;
  },

  incrementStats: (userId: number, gameType: string, increments: any): boolean => {
    const db = getDatabase();
    const existing = userStatsDb.findByUserAndGame(userId, gameType);

    if (!existing) {
      userStatsDb.create(userId, gameType);
    }

    const fields = Object.keys(increments).map(key => `${key} = ${key} + ?`).join(', ');
    const values = Object.values(increments);

    const stmt = db.prepare(`
      UPDATE user_statistics
      SET ${fields}, last_played = CURRENT_TIMESTAMP
      WHERE user_id = ? AND game_type = ?
    `);

    const result = stmt.run(...values, userId, gameType);
    return result.changes > 0;
  }
};

/**
 * Phase 2: Leaderboards database operations
 */
export const leaderboardDb = {
  updateEntry: (userId: number, username: string, gameType: string, category: string, value: number, period: string): boolean => {
    const db = getDatabase();

    // Calculate period dates
    const now = new Date();
    let periodStart: Date, periodEnd: Date;

    switch (period) {
      case 'daily':
        periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        periodEnd = new Date(periodStart.getTime() + 24 * 60 * 60 * 1000);
        break;
      case 'weekly':
        const dayOfWeek = now.getDay();
        periodStart = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);
        periodStart.setHours(0, 0, 0, 0);
        periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
        periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      default: // all_time
        periodStart = new Date(2024, 0, 1); // Platform start date
        periodEnd = new Date(2099, 11, 31); // Far future
    }

    const stmt = db.prepare(`
      INSERT OR REPLACE INTO leaderboards
      (user_id, username, game_type, category, value, period, period_start, period_end)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(userId, username, gameType, category, value, period,
                           periodStart.toISOString(), periodEnd.toISOString());
    return result.changes > 0;
  },

  getLeaderboard: (gameType: string, category: string, period: string, limit: number = 10): any[] => {
    const db = getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM leaderboards
      WHERE game_type = ? AND category = ? AND period = ?
      ORDER BY value DESC
      LIMIT ?
    `);

    return stmt.all(gameType, category, period, limit);
  },

  getUserRank: (userId: number, gameType: string, category: string, period: string): number => {
    const db = getDatabase();
    const stmt = db.prepare(`
      SELECT COUNT(*) + 1 as rank FROM leaderboards
      WHERE game_type = ? AND category = ? AND period = ? AND value > (
        SELECT COALESCE(value, 0) FROM leaderboards
        WHERE user_id = ? AND game_type = ? AND category = ? AND period = ?
      )
    `);

    const result = stmt.get(gameType, category, period, userId, gameType, category, period) as any;
    return result?.rank || 0;
  }
};

/**
 * Phase 2: Game Sessions database operations
 */
export const gameSessionDb = {
  startSession: (userId: number): any => {
    const db = getDatabase();

    // End any existing active session
    gameSessionDb.endActiveSession(userId);

    const stmt = db.prepare(`
      INSERT INTO game_sessions (user_id)
      VALUES (?)
    `);

    const result = stmt.run(userId);
    return gameSessionDb.findById(result.lastInsertRowid as number);
  },

  findById: (id: number): any => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM game_sessions WHERE id = ?');
    return stmt.get(id);
  },

  findActiveSession: (userId: number): any => {
    const db = getDatabase();
    const stmt = db.prepare('SELECT * FROM game_sessions WHERE user_id = ? AND is_active = 1 ORDER BY session_start DESC LIMIT 1');
    return stmt.get(userId);
  },

  updateSession: (sessionId: number, updates: any): boolean => {
    const db = getDatabase();
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);

    const stmt = db.prepare(`UPDATE game_sessions SET ${fields} WHERE id = ?`);
    const result = stmt.run(...values, sessionId);
    return result.changes > 0;
  },

  endActiveSession: (userId: number): boolean => {
    const db = getDatabase();
    const stmt = db.prepare(`
      UPDATE game_sessions
      SET is_active = 0, session_end = CURRENT_TIMESTAMP
      WHERE user_id = ? AND is_active = 1
    `);

    const result = stmt.run(userId);
    return result.changes > 0;
  },

  getUserSessions: (userId: number, limit: number = 20): any[] => {
    const db = getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM game_sessions
      WHERE user_id = ?
      ORDER BY session_start DESC
      LIMIT ?
    `);

    return stmt.all(userId, limit);
  }
};