import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, DiceGameState } from '@/types';
import { formatCurrency, GAME_CONFIG } from '@/lib/utils';
import { Dice1, Dice2, Dice3, Dice4, Dice5, Dice6, TrendingUp, TrendingDown } from 'lucide-react';

interface DiceControlsProps {
  user: User;
  gameState: DiceGameState | null;
  onStartGame: (betAmount: number, targetNumber: number, rollUnder: boolean) => Promise<void>;
  onRollDice: () => Promise<void>;
  loading: boolean;
  canRollDice: boolean;
  calculateWinChance: (targetNumber: number, rollUnder: boolean) => number;
  calculateMultiplier: (targetNumber: number, rollUnder: boolean) => number;
}

export function DiceControls({
  user,
  gameState,
  onStartGame,
  onRollDice,
  loading,
  canRollDice,
  calculateWinChance,
  calculateMultiplier
}: DiceControlsProps) {
  const [betAmount, setBetAmount] = useState<string>('1.00');
  const [targetNumber, setTargetNumber] = useState<number>(50);
  const [rollUnder, setRollUnder] = useState<boolean>(true);

  // Calculate current win chance and multiplier
  const winChance = calculateWinChance(targetNumber, rollUnder);
  const multiplier = calculateMultiplier(targetNumber, rollUnder);

  const handleBetAmountChange = (value: string) => {
    // Allow only valid decimal numbers
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setBetAmount(value);
    }
  };

  const handleTargetNumberChange = (value: string) => {
    const num = parseInt(value);
    if (!isNaN(num) && num >= 2 && num <= 98) {
      setTargetNumber(num);
    }
  };

  const handleStartGame = async () => {
    const amount = parseFloat(betAmount);
    if (amount > 0 && amount <= user.usdt_balance) {
      await onStartGame(amount, targetNumber, rollUnder);
    }
  };

  const isValidBet = () => {
    const amount = parseFloat(betAmount);
    return amount > 0 && amount <= user.usdt_balance && amount >= 0.01;
  };

  const canStartGame = !gameState && isValidBet() && !loading;
  const showRollButton = gameState && canRollDice && !loading;

  // Calculate max bet amount (minimum of user balance and game max bet)
  const maxBetAmount = Math.min(user.usdt_balance, GAME_CONFIG.MAX_BET);

  return (
    <div className="space-y-4">
      {/* Bet Amount */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Bet Amount</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="relative">
            <Input
              type="text"
              value={betAmount}
              onChange={(e) => handleBetAmountChange(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white pr-12"
              placeholder="0.00"
              disabled={!!gameState}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-orange-400 text-sm font-medium">
              ₿
            </span>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setBetAmount('0.01')}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              Min
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setBetAmount((parseFloat(betAmount) * 2).toFixed(2))}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              2x
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setBetAmount(maxBetAmount.toFixed(2))}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              Max
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Target Number */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Target Number</CardTitle>
          <CardDescription className="text-gray-400 text-xs">
            Choose a number between 2-98
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="relative">
            <Input
              type="number"
              min="2"
              max="98"
              value={targetNumber}
              onChange={(e) => handleTargetNumberChange(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
              disabled={!!gameState}
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTargetNumber(2)}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              Min (2)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTargetNumber(50)}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              50
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTargetNumber(98)}
              className="flex-1 bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600"
              disabled={!!gameState}
            >
              Max (98)
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Roll Direction */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Roll Direction</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={rollUnder ? 'under' : 'over'} onValueChange={(value) => setRollUnder(value === 'under')}>
            <TabsList className="grid w-full grid-cols-2 bg-gray-700">
              <TabsTrigger
                value="under"
                className="data-[state=active]:bg-red-600 data-[state=active]:text-white"
                disabled={!!gameState}
              >
                <TrendingDown className="h-4 w-4 mr-1" />
                Roll Under
              </TabsTrigger>
              <TabsTrigger
                value="over"
                className="data-[state=active]:bg-green-600 data-[state=active]:text-white"
                disabled={!!gameState}
              >
                <TrendingUp className="h-4 w-4 mr-1" />
                Roll Over
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>

      {/* Game Stats */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardHeader className="pb-3">
          <CardTitle className="text-white text-sm">Game Stats</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-400 text-sm">Win Chance:</span>
            <span className="text-white text-sm font-medium">{winChance}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400 text-sm">Multiplier:</span>
            <span className="text-green-400 text-sm font-medium">{multiplier.toFixed(4)}x</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400 text-sm">Potential Profit:</span>
            <span className="text-yellow-400 text-sm font-medium">
              {formatCurrency(parseFloat(betAmount || '0') * (multiplier - 1))} ₿
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="space-y-2">
        {canStartGame && (
          <Button
            onClick={handleStartGame}
            disabled={!canStartGame || loading}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3"
          >
            {loading ? 'Starting...' : `Bet ${formatCurrency(parseFloat(betAmount || '0'))} ₿`}
          </Button>
        )}

        {showRollButton && (
          <Button
            onClick={onRollDice}
            disabled={!canRollDice || loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3"
          >
            {loading ? 'Rolling...' : '🎲 Roll Dice'}
          </Button>
        )}
      </div>

      {/* Balance Display */}
      <div className="text-center">
        <div className="text-gray-400 text-xs">Balance</div>
        <div className="text-white font-medium">{formatCurrency(user.usdt_balance)} ₿</div>
      </div>
    </div>
  );
}
