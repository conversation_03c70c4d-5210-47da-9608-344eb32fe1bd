import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb } from '@/lib/database';

interface StatsData {
  totalProfit: number;
  totalWagered: number;
  totalWins: number;
  totalLosses: number;
  winRate: number;
  chartData: Array<{
    gameId: number;
    timestamp: string;
    profit: number;
    cumulativeProfit: number;
    betAmount: number;
    multiplier: number;
    gameType: string;
  }>;
}

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    // Get session start time from query parameter
    const { sessionStartTime } = req.query;

    // Get all completed games for the user
    let games = gameDb.findByUserId(user.id, 1000).filter(game =>
      ['won', 'lost', 'cashed_out'].includes(game.status)
    );

    // Filter by session start time if provided
    if (sessionStartTime && typeof sessionStartTime === 'string') {
      const sessionStart = new Date(sessionStartTime);

      games = games.filter(game => {
        // Parse the game timestamp - SQLite CURRENT_TIMESTAMP stores UTC but without timezone indicator
        const gameTimestamp = game.created_at!;

        // SQLite CURRENT_TIMESTAMP format: "2025-05-26 19:00:50"
        // This is actually UTC time but JavaScript interprets it as local time
        // We need to explicitly treat it as UTC
        let gameDate: Date;
        if (gameTimestamp.includes(' ') && !gameTimestamp.includes('T')) {
          // SQLite format: "YYYY-MM-DD HH:MM:SS" - treat as UTC
          gameDate = new Date(gameTimestamp.replace(' ', 'T') + 'Z');
        } else if (gameTimestamp.includes('T') && !gameTimestamp.includes('Z') && !gameTimestamp.includes('+')) {
          // ISO format without timezone - treat as UTC
          gameDate = new Date(gameTimestamp + 'Z');
        } else {
          gameDate = new Date(gameTimestamp);
        }

        return gameDate >= sessionStart;
      });
    }

    // Calculate basic stats
    const totalWins = games.filter(game => game.profit > 0).length;
    const totalLosses = games.filter(game => game.profit <= 0).length;
    const totalGames = games.length;
    const winRate = totalGames > 0 ? (totalWins / totalGames) * 100 : 0;

    const totalProfit = games.reduce((sum, game) => sum + game.profit, 0);
    const totalWagered = games.reduce((sum, game) => sum + game.bet_amount, 0);

    // Create chart data with cumulative profit
    let cumulativeProfit = 0;
    const chartData = games
      .sort((a, b) => new Date(a.created_at!).getTime() - new Date(b.created_at!).getTime())
      .map(game => {
        cumulativeProfit += game.profit;
        return {
          gameId: game.id!,
          timestamp: game.created_at!,
          profit: game.profit,
          cumulativeProfit: Number(cumulativeProfit.toFixed(8)),
          betAmount: game.bet_amount,
          multiplier: game.current_multiplier,
          gameType: game.game_type
        };
      });

    const stats: StatsData = {
      totalProfit: Number(totalProfit.toFixed(8)),
      totalWagered: Number(totalWagered.toFixed(8)),
      totalWins,
      totalLosses,
      winRate: Number(winRate.toFixed(2)),
      chartData
    };

    res.status(200).json({ success: true, stats });
  } catch (error) {
    console.error('Stats API error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});
