import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_type, limit } = req.query;

    const gameLimit = limit ? parseInt(limit as string) : 50;
    const gameTypeFilter = game_type as string | undefined;

    if (gameLimit > 100) {
      return res.status(400).json({
        success: false,
        error: 'Limit cannot exceed 100'
      });
    }

    const games = gameDb.findByUserId(user.id, gameLimit, gameTypeFilter);

    // Remove sensitive data from response
    const sanitizedGames = games.map(game => ({
      ...game,
      server_seed: game.status === 'active' ? 'hidden' : game.server_seed,
      // Hide mine positions for active mines games
      ...(game.game_type === 'mines' && game.status === 'active'
        ? { mine_positions: [] }
        : {})
    }));

    return res.status(200).json({
      success: true,
      games: sanitizedGames,
      total: sanitizedGames.length
    });
  } catch (error) {
    console.error('Game history API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
