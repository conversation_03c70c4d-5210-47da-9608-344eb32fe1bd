# Sound Files

This directory contains placeholder references for sound files used in the Mines game.

In a production environment, you would replace these with actual audio files:

- `click.mp3` - Sound when clicking a cell
- `reveal.mp3` - Sound when revealing a safe cell (gem)
- `mine.mp3` - Sound when hitting a mine
- `cashout.mp3` - Sound when cashing out
- `win.mp3` - Sound when winning a game
- `lose.mp3` - Sound when losing a game

## Audio File Requirements

- Format: MP3 or OGG for web compatibility
- Duration: 0.5-2 seconds for UI sounds
- Volume: Normalized to prevent audio clipping
- Quality: 44.1kHz, 16-bit minimum

## Implementation Note

The current sound manager in `/lib/sounds.ts` will gracefully handle missing audio files by logging warnings to the console without breaking the game functionality.
