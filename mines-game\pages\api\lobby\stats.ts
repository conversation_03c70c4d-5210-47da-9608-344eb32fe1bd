import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, getDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Get database instance
    const db = getDatabase();

    // Get today's date for filtering
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayISO = today.toISOString();

    // Get total games played today
    const totalGamesToday = db.prepare(`
      SELECT COUNT(*) as count
      FROM games
      WHERE created_at >= ?
    `).get(todayISO) as { count: number };

    // Get total bets amount today
    const totalBetsToday = db.prepare(`
      SELECT COALESCE(SUM(bet_amount), 0) as total
      FROM games
      WHERE created_at >= ?
    `).get(todayISO) as { total: number };

    // Get biggest win today
    const biggestWinToday = db.prepare(`
      SELECT COALESCE(MAX(profit), 0) as max_profit
      FROM games
      WHERE created_at >= ? AND profit > 0
    `).get(todayISO) as { max_profit: number };

    // Get recent winners (last 10 profitable games)
    const recentWinners = db.prepare(`
      SELECT
        g.id,
        u.username,
        g.game_type,
        g.current_multiplier,
        g.profit,
        g.created_at
      FROM games g
      JOIN users u ON g.user_id = u.id
      WHERE g.profit > 0 AND g.status IN ('won', 'cashed_out')
      ORDER BY g.created_at DESC
      LIMIT 10
    `).all();

    // Format recent winners data
    const formattedWinners = recentWinners.map((winner: any, index: number) => ({
      id: winner.id.toString(),
      username: `${winner.username.substring(0, 6)}***`, // Anonymize username
      game: winner.game_type.charAt(0).toUpperCase() + winner.game_type.slice(1),
      multiplier: parseFloat(winner.current_multiplier.toFixed(2)),
      winAmount: parseFloat(winner.profit.toFixed(2)),
      timestamp: winner.created_at
    }));

    // Generate mock online players count (since we don't have real-time tracking)
    const baseOnlineCount = 150;
    const variance = Math.floor(Math.random() * 100) - 50; // ±50 variance
    const playersOnline = Math.max(50, baseOnlineCount + variance);

    const stats = {
      playersOnline,
      totalBetsToday: Math.floor(totalBetsToday.total),
      biggestWinToday: parseFloat(biggestWinToday.max_profit.toFixed(2)),
      totalGamesPlayed: totalGamesToday.count,
      recentWinners: formattedWinners
    };

    return res.status(200).json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Failed to get lobby stats:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get lobby statistics'
    });
  }
});
