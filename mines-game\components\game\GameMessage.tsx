import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

export type GameMessageType =
  | 'boom'
  | 'win'
  | 'lost'
  | 'cashout'
  | 'perfect_win'
  | 'big_win';

interface GameMessageProps {
  type: GameMessageType;
  visible: boolean;
  onComplete?: () => void;
  multiplier?: number;
  profit?: number;
}

const messageConfig = {
  boom: {
    text: '💥 BOOM!',
    subtext: 'You hit a mine!',
    className: 'text-red-400 animate-shake',
    bgClassName: 'bg-red-500/20 border-red-500/50',
    duration: 750, // 0.75 seconds
  },
  win: {
    text: '🎉 YOU WON!',
    subtext: 'Perfect game!',
    className: 'text-green-400 animate-bounce',
    bgClassName: 'bg-green-500/20 border-green-500/50',
    duration: 1000, // 1 second
  },
  lost: {
    text: '💔 YOU LOST!',
    subtext: 'Better luck next time!',
    className: 'text-red-400 animate-shake',
    bgClassName: 'bg-red-500/20 border-red-500/50',
    duration: 1000, // 1 second
  },
  cashout: {
    text: '💰 CASHED OUT!',
    subtext: 'Smart move!',
    className: 'text-yellow-400 animate-pulse-glow',
    bgClassName: 'bg-yellow-500/20 border-yellow-500/50',
    duration: 1000, // 1 second
  },
  perfect_win: {
    text: '🏆 PERFECT!',
    subtext: 'All safe cells found!',
    className: 'text-purple-400 animate-celebration',
    bgClassName: 'bg-purple-500/20 border-purple-500/50',
    duration: 1000, // 1 second
  },
  big_win: {
    text: '🚀 BIG WIN!',
    subtext: 'Amazing multiplier!',
    className: 'text-orange-400 animate-big-win',
    bgClassName: 'bg-orange-500/20 border-orange-500/50',
    duration: 1000, // 1 second
  },
};

export function GameMessage({
  type,
  visible,
  onComplete,
  multiplier,
  profit
}: GameMessageProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  const config = messageConfig[type];

  // Safety check - if config is undefined, don't render anything
  if (!config) {
    console.warn(`GameMessage: Unknown message type '${type}'`);
    return null;
  }

  useEffect(() => {
    if (visible && config) {
      setIsVisible(true);
      setIsAnimating(true);

      const timer = setTimeout(() => {
        setIsAnimating(false);
        setTimeout(() => {
          setIsVisible(false);
          onComplete?.();
        }, 300); // Exit animation duration
      }, config.duration);

      return () => clearTimeout(timer);
    }
  }, [visible, config, onComplete]);

  if (!isVisible) return null;

  const formatProfit = (amount: number) => {
    return amount >= 0 ? `+${amount.toFixed(2)}` : amount.toFixed(2);
  };

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center pointer-events-none",
      "transition-all duration-300",
      isAnimating ? "opacity-100 scale-100" : "opacity-0 scale-95"
    )}>
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Message container */}
      <div className={cn(
        "relative p-8 rounded-2xl border-2 backdrop-blur-md",
        "transform transition-all duration-500",
        config.bgClassName,
        isAnimating ? "scale-100 rotate-0" : "scale-110 rotate-1"
      )}>
        {/* Main message */}
        <div className={cn(
          "text-6xl font-bold text-center mb-4",
          config.className
        )}>
          {config.text}
        </div>

        {/* Subtext */}
        <div className="text-xl text-center text-gray-300 mb-4">
          {config.subtext}
        </div>

        {/* Additional info for wins/cashouts/losses */}
        {(type === 'cashout' || type === 'win' || type === 'lost' || type === 'perfect_win' || type === 'big_win') && (
          <div className="text-center space-y-2">
            {multiplier && (
              <div className="text-2xl font-bold text-yellow-400">
                {multiplier.toFixed(2)}x Multiplier
              </div>
            )}
            {profit !== undefined && (
              <div className={cn(
                "text-xl font-semibold",
                profit >= 0 ? "text-green-400" : "text-red-400"
              )}>
                {formatProfit(profit)} USDT
              </div>
            )}
          </div>
        )}

        {/* Particle effects for big wins */}
        {(type === 'perfect_win' || type === 'big_win') && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(12)].map((_, i) => (
              <div
                key={i}
                className={cn(
                  "absolute w-2 h-2 bg-yellow-400 rounded-full",
                  "animate-confetti"
                )}
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`,
                }}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
