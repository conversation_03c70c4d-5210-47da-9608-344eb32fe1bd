import React, { useState } from 'react';
import { GameState, User } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RotateCcw } from 'lucide-react';
import { formatCurrency, GAME_CONFIG } from '@/lib/utils';
import { useUniversalGame } from '@/contexts/UniversalGameContext';

interface GameControlsProps {
  user: User;
  gameState: GameState | null;
  onStartGame: (betAmount: number, mineCount: number) => void;
  onCashOut: () => void;
  loading: boolean;
}

export function GameControls({
  user,
  gameState,
  onStartGame,
  onCashOut,
  loading
}: GameControlsProps) {
  const { forceResetActiveGame } = useUniversalGame();

  const [betAmount, setBetAmount] = useState(1);
  const [mineCount, setMineCount] = useState(3);

  const isGameActive = gameState?.status === 'active';
  const revealedCells = (gameState as any)?.revealed_cells || [];
  const canCashOut = isGameActive && revealedCells.length > 0;
  const maxBet = Math.min(user.usdt_balance, GAME_CONFIG.MAX_BET);

  const isButtonDisabled = loading || betAmount <= 0 || betAmount > user.usdt_balance;

  const handleStartGame = () => {
    if (betAmount <= 0 || betAmount > user.usdt_balance) return;
    onStartGame(betAmount, mineCount);
  };

  const handleBetAmountChange = (value: string) => {
    const amount = parseFloat(value) || 0;
    setBetAmount(Math.max(0, Math.min(amount, maxBet)));
  };

  const handleMineCountChange = (value: string) => {
    const count = parseInt(value) || 1;
    setMineCount(Math.max(GAME_CONFIG.MIN_MINES, Math.min(count, GAME_CONFIG.MAX_MINES)));
  };

  const quickBetAmounts = [0.1, 0.5, 1, 5, 10].filter(amount => amount <= maxBet);
  const quickMineAmounts = [1, 3, 5, 10, 15];

  // Percentage bet functions
  const setBetPercentage = (percentage: number) => {
    const amount = (user.usdt_balance * percentage) / 100;
    setBetAmount(Math.min(amount, maxBet));
  };

  const doubleBet = () => {
    setBetAmount(Math.min(betAmount * 2, maxBet));
  };



  const halveBet = () => {
    setBetAmount(Math.max(betAmount / 2, GAME_CONFIG.MIN_BET));
  };

  const handleResetGame = async () => {
    if (confirm('Are you sure you want to reset the current game? Your bet will be refunded.')) {
      const success = await forceResetActiveGame();
      if (success) {
        // Game has been reset, user can now start a new game
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* Game Status */}
      {isGameActive && (
        <Card className="bg-gray-800/80 border-gray-600 backdrop-blur-sm">
          <CardContent className="pt-4 pb-4">
            <div className="text-center space-y-3">
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3">
                <div className="text-sm text-green-400 mb-1">Current Multiplier</div>
                <div className="text-2xl font-bold text-green-400">
                  {gameState.current_multiplier.toFixed(2)}x
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-gray-400 mb-1">Potential Profit</div>
                <div className={`text-lg font-semibold ${gameState.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {gameState.profit >= 0 ? '+' : ''}{formatCurrency(gameState.profit)} USDT
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reset Game Button - Only show when there's an active game */}
      {isGameActive && (
        <Card className="bg-red-900/20 border-red-600/30 backdrop-blur-sm">
          <CardContent className="pt-4 pb-4">
            <div className="text-center space-y-3">
              <div className="text-sm text-red-400 mb-2">
                Game in Progress
              </div>
              <Button
                onClick={handleResetGame}
                disabled={loading}
                variant="outline"
                className="w-full border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset Game (Refund Bet)
              </Button>
              <p className="text-xs text-red-300">
                This will cancel the current game and refund your bet amount
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Game Controls */}
      <Card className="bg-gray-800/80 border-gray-600 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="space-y-4">
              {!isGameActive ? (
                <>
                  {/* Bet Amount */}
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-300">
                      Bet Amount
                    </label>
                    <div className="relative">
                      <Input
                        type="number"
                        min={GAME_CONFIG.MIN_BET}
                        max={maxBet}
                        step="0.01"
                        value={betAmount}
                        onChange={(e) => handleBetAmountChange(e.target.value)}
                        className="bg-gray-700/50 border-gray-600 text-white pr-12"
                        placeholder="0.00000000"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">
                        ₿
                      </div>
                    </div>

                    {/* Bet Amount Controls */}
                    <div className="grid grid-cols-4 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={halveBet}
                        className="text-xs border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        ½
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={doubleBet}
                        className="text-xs border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        2×
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setBetPercentage(25)}
                        className="text-xs border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        25%
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setBetAmount(maxBet)}
                        className="text-xs border-gray-600 text-gray-300 hover:bg-gray-600"
                      >
                        Max
                      </Button>
                    </div>

                    <div className="text-xs text-gray-400">
                      {formatCurrency(user.usdt_balance)} ₿
                    </div>
                  </div>

                  {/* Mine Count */}
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-300">
                      Mines
                    </label>
                    <Select value={mineCount.toString()} onValueChange={(value) => setMineCount(parseInt(value))}>
                      <SelectTrigger className="bg-gray-700/50 border-gray-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600">
                        {Array.from({ length: GAME_CONFIG.MAX_MINES }, (_, i) => i + 1).map((count) => (
                          <SelectItem key={count} value={count.toString()} className="text-white hover:bg-gray-700">
                            {count}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Start Game Button */}
                  <Button
                    onClick={handleStartGame}
                    disabled={isButtonDisabled}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-lg"
                  >
                    {loading ? 'Starting...' : 'Bet'}
                  </Button>
                </>
              ) : (
                <>
                  {/* Cash Out Button */}
                  <Button
                    onClick={onCashOut}
                    disabled={loading || !canCashOut}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-lg"
                  >
                    {loading ? 'Cashing Out...' : canCashOut ? 'Cash Out' : 'Reveal a cell first'}
                  </Button>

                  {canCashOut && (
                    <div className="text-center text-sm text-gray-300">
                      Cash out now to secure your profit!
                    </div>
                  )}
                </>
              )}
            </div>
        </CardContent>
      </Card>

      {/* Game Stats */}
      {!isGameActive && (
        <Card className="bg-gray-800/50 border-gray-600">
          <CardContent className="pt-4 pb-4">
            <div className="text-xs text-gray-400 space-y-2">
              <div className="flex justify-between">
                <span>House Edge:</span>
                <span>{(GAME_CONFIG.HOUSE_EDGE * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span>Min Bet:</span>
                <span>{formatCurrency(GAME_CONFIG.MIN_BET)} ₿</span>
              </div>
              <div className="flex justify-between">
                <span>Max Bet:</span>
                <span>{formatCurrency(GAME_CONFIG.MAX_BET)} ₿</span>
              </div>
              <div className="flex justify-between">
                <span>Max Multiplier:</span>
                <span>1000.00x</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
