import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb, userDb } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Find any active game for the user
    const activeGame = gameDb.findActiveByUserId(user.id);

    if (!activeGame) {
      return res.status(200).json({
        success: true,
        message: 'No active game to reset'
      });
    }

    // Update the game status to cancelled
    gameDb.update(activeGame.id, {
      status: 'cancelled'
    });

    // Refund the bet amount to the user
    const currentUser = userDb.findById(user.id);
    if (currentUser) {
      userDb.updateBalance(user.id, 'USDT', currentUser.usdt_balance + activeGame.bet_amount);
    }

    return res.status(200).json({
      success: true,
      message: 'Active game reset successfully',
      refundAmount: activeGame.bet_amount
    });

  } catch (error) {
    console.error('Reset active game API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
