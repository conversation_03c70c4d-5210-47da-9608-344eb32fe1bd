import { GameType, GameConfig, GameProviderInterface, GameState, GameAction, BaseGameState } from '@/types';
import { generateServerSeed, generateClientSeed } from '@/lib/crypto';

/**
 * Abstract base class for all game providers
 * Implements common functionality and enforces interface compliance
 */
export abstract class BaseGameProvider<T extends GameState = GameState> implements GameProviderInterface<T> {
  public abstract readonly gameType: GameType;
  public abstract readonly config: GameConfig;

  /**
   * Validate common game parameters
   */
  protected validateBaseParams(betAmount: number): boolean {
    if (typeof betAmount !== 'number' || betAmount <= 0) {
      return false;
    }

    if (betAmount < this.config.minBet || betAmount > this.config.maxBet) {
      return false;
    }

    return true;
  }

  /**
   * Generate base game data common to all games
   */
  protected generateBaseGameData(userId: number, betAmount: number, clientSeed?: string): Partial<BaseGameState> {
    return {
      user_id: userId,
      game_type: this.gameType,
      bet_amount: betAmount,
      current_multiplier: 1.0,
      status: 'active',
      server_seed: generateServerSeed(),
      client_seed: clientSeed || generateClientSeed(),
      profit: 0
    };
  }

  /**
   * Calculate profit based on bet amount and multiplier
   */
  protected calculateProfit(betAmount: number, multiplier: number): number {
    return betAmount * multiplier - betAmount;
  }

  /**
   * Apply house edge to multiplier
   */
  protected applyHouseEdge(multiplier: number): number {
    return multiplier * (1 - this.config.houseEdge);
  }

  /**
   * Validate that multiplier doesn't exceed maximum
   */
  protected validateMultiplier(multiplier: number): boolean {
    return multiplier <= this.config.maxMultiplier;
  }

  /**
   * Generate provably fair hash for verification
   */
  protected generateGameHash(serverSeed: string, clientSeed: string, nonce: number = 0): string {
    const crypto = require('crypto');
    const combined = `${serverSeed}:${clientSeed}:${nonce}`;
    return crypto.createHash('sha256').update(combined).digest('hex');
  }

  /**
   * Generate random number from seeds (0-1)
   */
  protected generateRandomFromSeeds(serverSeed: string, clientSeed: string, nonce: number = 0): number {
    const hash = this.generateGameHash(serverSeed, clientSeed, nonce);
    // Use first 8 characters of hash to generate number between 0-1
    const hexValue = hash.substring(0, 8);
    const intValue = parseInt(hexValue, 16);
    return intValue / 0xffffffff;
  }

  /**
   * Generate random integer within range using provably fair method
   */
  protected generateRandomInt(min: number, max: number, serverSeed: string, clientSeed: string, nonce: number = 0): number {
    const random = this.generateRandomFromSeeds(serverSeed, clientSeed, nonce);
    return Math.floor(random * (max - min + 1)) + min;
  }

  /**
   * Generate array of random integers (useful for mines, card games, etc.)
   */
  protected generateRandomArray(count: number, min: number, max: number, serverSeed: string, clientSeed: string, startNonce: number = 0): number[] {
    const result: number[] = [];
    const range = max - min + 1;

    for (let i = 0; i < count; i++) {
      const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);
      const value = Math.floor(random * range) + min;
      result.push(value);
    }

    return result;
  }

  /**
   * Shuffle array using Fisher-Yates algorithm with provably fair randomness
   */
  protected shuffleArray<U>(array: U[], serverSeed: string, clientSeed: string, startNonce: number = 0): U[] {
    const shuffled = [...array];

    for (let i = shuffled.length - 1; i > 0; i--) {
      const random = this.generateRandomFromSeeds(serverSeed, clientSeed, startNonce + i);
      const j = Math.floor(random * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    return shuffled;
  }

  /**
   * Validate game state belongs to user
   */
  protected validateGameOwnership(gameState: T, userId: number): boolean {
    return gameState.user_id === userId;
  }

  /**
   * Check if game is in active state
   */
  protected isGameActive(gameState: T): boolean {
    return gameState.status === 'active';
  }

  /**
   * Log game action for debugging/auditing
   */
  protected logGameAction(gameState: T, action: GameAction, result?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${this.gameType.toUpperCase()}] Game ${gameState.id}: ${action.type}`, {
        payload: action.payload,
        result,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Abstract methods that must be implemented by each game
  public abstract validateGameParams(params: any): boolean;
  public abstract calculateMultiplier(gameState: T, params?: any): number;
  public abstract generateGameData(params: any): Partial<T>;
  public abstract processGameAction(gameState: T, action: GameAction): Promise<T>;
}

/**
 * Game action types enum for consistency
 */
export enum GameActionType {
  START_GAME = 'START_GAME',
  MAKE_MOVE = 'MAKE_MOVE',
  CASH_OUT = 'CASH_OUT',
  END_GAME = 'END_GAME',
  CANCEL_GAME = 'CANCEL_GAME'
}

/**
 * Common game errors
 */
export class GameError extends Error {
  constructor(
    message: string,
    public code: string,
    public gameType: GameType
  ) {
    super(message);
    this.name = 'GameError';
  }
}

export class InvalidGameParamsError extends GameError {
  constructor(gameType: GameType, message: string = 'Invalid game parameters') {
    super(message, 'INVALID_PARAMS', gameType);
  }
}

export class GameNotActiveError extends GameError {
  constructor(gameType: GameType, message: string = 'Game is not active') {
    super(message, 'GAME_NOT_ACTIVE', gameType);
  }
}

export class InsufficientFundsError extends GameError {
  constructor(gameType: GameType, message: string = 'Insufficient funds') {
    super(message, 'INSUFFICIENT_FUNDS', gameType);
  }
}

export class GameActionError extends GameError {
  constructor(message: string, gameType: GameType) {
    super(message, 'INVALID_ACTION', gameType);
  }
}
