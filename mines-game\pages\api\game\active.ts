import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb } from '@/lib/database';
import { hashServerSeed } from '@/lib/crypto';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const game = gameDb.findActiveByUserId(user.id);

    if (game) {
      // Hide sensitive information for active games
      const clientGame = {
        ...game,
        server_seed: hashServerSeed(game.server_seed) // Only send hash
      };

      // Hide mine positions for active mines games
      if (game.game_type === 'mines' && game.status === 'active') {
        (clientGame as any).mine_positions = [];
      }

      return res.status(200).json({
        success: true,
        game: clientGame
      });
    } else {
      return res.status(200).json({
        success: true,
        game: null
      });
    }
  } catch (error) {
    console.error('Active game API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
