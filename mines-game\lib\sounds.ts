import { SoundType } from '@/types';

class SoundManager {
  private sounds: Map<SoundType, HTMLAudioElement> = new Map();
  private enabled: boolean = true;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeSounds();
    }
  }

  private initializeSounds() {
    const soundFiles: Record<SoundType, string> = {
      click: '/sounds/click.mp3',
      reveal: '/sounds/reveal.mp3',
      mine: '/sounds/mine.mp3',
      cashout: '/sounds/cashout.mp3',
      win: '/sounds/win.mp3',
      lose: '/sounds/lose.mp3',
    };

    Object.entries(soundFiles).forEach(([type, src]) => {
      const audio = new Audio(src);
      audio.preload = 'auto';
      audio.volume = 0.5;
      this.sounds.set(type as SoundType, audio);
    });
  }

  play(type: SoundType) {
    if (!this.enabled) return;

    const sound = this.sounds.get(type);
    if (sound) {
      sound.currentTime = 0;
      sound.play().catch(error => {
        console.warn(`Failed to play sound ${type}:`, error);
      });
    }
  }

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  setVolume(volume: number) {
    this.sounds.forEach(sound => {
      sound.volume = Math.max(0, Math.min(1, volume));
    });
  }
}

export const soundManager = new SoundManager();
