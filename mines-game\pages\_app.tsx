import '@/styles/globals.css';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { AuthProvider } from '@/contexts/AuthContext';
import { UniversalGameProvider } from '@/contexts/UniversalGameContext';
import { MinesGameProvider } from '@/contexts/MinesGameContext';
import { DiceGameProvider } from '@/contexts/DiceGameContext';
import { ToastProvider } from '@/components/ui/toast';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Head>
        <title>BetOctave - Provably Fair Crypto Gambling</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="description" content="Experience the thrill of provably fair crypto gambling with multiple games, transparent mechanics, and instant payouts." />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <ToastProvider>
        <AuthProvider>
          <UniversalGameProvider>
            <MinesGameProvider>
              <DiceGameProvider>
                <Component {...pageProps} />
              </DiceGameProvider>
            </MinesGameProvider>
          </UniversalGameProvider>
        </AuthProvider>
      </ToastProvider>
    </>
  );
}
