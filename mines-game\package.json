{"name": "betoctave", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "next": "^15.3.2", "node-fetch": "^2.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}