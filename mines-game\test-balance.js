const { initDatabase, userDb } = require('./lib/database.js');

try {
  initDatabase();
  console.log('Database initialized');
  
  // Add balance to user ID 2
  const user = userDb.findById(2);
  console.log('Current user:', user);
  
  if (user) {
    userDb.updateBalance(2, 'USDT', 100);
    const updatedUser = userDb.findById(2);
    console.log('Updated user balance:', updatedUser.usdt_balance);
  } else {
    console.log('User not found');
  }
} catch (error) {
  console.error('Error:', error);
}
