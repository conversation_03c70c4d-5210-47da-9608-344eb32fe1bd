import React, { useState, useEffect } from 'react';
import { DiceGameState } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import { TrendingUp, TrendingDown, Target } from 'lucide-react';

interface DiceDisplayProps {
  gameState: DiceGameState | null;
  loading: boolean;
}

export function DiceDisplay({ gameState, loading }: DiceDisplayProps) {
  const [isRolling, setIsRolling] = useState(false);
  const [displayResult, setDisplayResult] = useState<number | null>(null);

  useEffect(() => {
    if (gameState?.result !== undefined && gameState.result !== displayResult) {
      // Start rolling animation
      setIsRolling(true);
      
      // Show result after animation
      setTimeout(() => {
        setDisplayResult(gameState.result!);
        setIsRolling(false);
      }, 1500);
    }
  }, [gameState?.result, displayResult]);

  // Reset display when game resets
  useEffect(() => {
    if (!gameState) {
      setDisplayResult(null);
      setIsRolling(false);
    }
  }, [gameState]);

  const renderDiceNumber = (number: number | null) => {
    if (number === null) return '?';
    return number.toString();
  };

  const getDiceColor = () => {
    if (!gameState || displayResult === null) return 'text-gray-400';
    
    const won = gameState.status === 'won';
    return won ? 'text-green-400' : 'text-red-400';
  };

  const getResultMessage = () => {
    if (!gameState || displayResult === null) return null;
    
    const won = gameState.status === 'won';
    const direction = gameState.roll_under ? 'under' : 'over';
    
    if (won) {
      return (
        <div className="text-center space-y-2">
          <div className="text-green-400 font-bold text-lg">🎉 You Won!</div>
          <div className="text-white text-sm">
            Rolled {displayResult} ({direction} {gameState.target_number})
          </div>
          <div className="text-green-400 font-medium">
            +{formatCurrency(gameState.profit)} ₿
          </div>
        </div>
      );
    } else {
      return (
        <div className="text-center space-y-2">
          <div className="text-red-400 font-bold text-lg">💥 You Lost!</div>
          <div className="text-white text-sm">
            Rolled {displayResult} ({direction} {gameState.target_number})
          </div>
          <div className="text-red-400 font-medium">
            {formatCurrency(gameState.profit)} ₿
          </div>
        </div>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Dice Display */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* Dice Visual */}
            <div className="relative">
              <div className={`
                w-32 h-32 mx-auto rounded-xl border-4 border-gray-600 
                bg-gradient-to-br from-gray-700 to-gray-800 
                flex items-center justify-center text-6xl font-bold
                ${isRolling ? 'animate-bounce' : ''}
                ${getDiceColor()}
                transition-all duration-300
              `}>
                {isRolling ? (
                  <div className="animate-spin">🎲</div>
                ) : (
                  renderDiceNumber(displayResult)
                )}
              </div>
              
              {/* Rolling indicator */}
              {isRolling && (
                <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
                  <div className="text-yellow-400 text-sm font-medium animate-pulse">
                    Rolling...
                  </div>
                </div>
              )}
            </div>

            {/* Game Info */}
            {gameState && (
              <div className="space-y-4">
                {/* Target and Direction */}
                <div className="flex items-center justify-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <Target className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-400">Target:</span>
                    <span className="text-white font-medium">{gameState.target_number}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    {gameState.roll_under ? (
                      <>
                        <TrendingDown className="h-4 w-4 text-red-400" />
                        <span className="text-red-400">Roll Under</span>
                      </>
                    ) : (
                      <>
                        <TrendingUp className="h-4 w-4 text-green-400" />
                        <span className="text-green-400">Roll Over</span>
                      </>
                    )}
                  </div>
                </div>

                {/* Bet Info */}
                <div className="flex justify-center space-x-6 text-sm">
                  <div className="text-center">
                    <div className="text-gray-400">Bet Amount</div>
                    <div className="text-white font-medium">{formatCurrency(gameState.bet_amount)} ₿</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-gray-400">Multiplier</div>
                    <div className="text-yellow-400 font-medium">{gameState.current_multiplier.toFixed(4)}x</div>
                  </div>
                </div>

                {/* Result Message */}
                {displayResult !== null && getResultMessage()}
              </div>
            )}

            {/* Waiting State */}
            {!gameState && (
              <div className="text-center space-y-2">
                <div className="text-gray-400 text-lg">Ready to Roll</div>
                <div className="text-gray-500 text-sm">Place your bet to start playing</div>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="text-center space-y-2">
                <div className="text-yellow-400 text-lg animate-pulse">Processing...</div>
                <div className="text-gray-500 text-sm">Please wait</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Game Rules */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-4">
          <div className="text-center space-y-2">
            <h3 className="text-white font-medium text-sm">How to Play</h3>
            <div className="text-gray-400 text-xs space-y-1">
              <p>1. Set your bet amount and target number (2-98)</p>
              <p>2. Choose "Roll Under" or "Roll Over"</p>
              <p>3. Click "Roll Dice" to see if you win!</p>
              <p>4. Higher risk = Higher reward</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Provably Fair Info */}
      <Card className="bg-gray-800/50 border-gray-600">
        <CardContent className="p-4">
          <div className="text-center space-y-2">
            <h3 className="text-green-400 font-medium text-sm">🔒 Provably Fair</h3>
            <div className="text-gray-400 text-xs">
              Every roll is cryptographically verifiable using SHA-256 hashing
            </div>
            {gameState && (
              <div className="text-gray-500 text-xs font-mono">
                Game ID: #{gameState.id?.toString().slice(-8) || 'N/A'}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
