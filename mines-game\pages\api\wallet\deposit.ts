import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { userDb, transactionDb, initDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { currency, amount } = req.body;

    // Validate input
    if (!currency || !['USDT', 'LTC'].includes(currency)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid currency. Must be USDT or LTC'
      });
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount'
      });
    }

    // Validate amount limits
    const minDeposit = currency === 'USDT' ? 1 : 0.001;
    const maxDeposit = currency === 'USDT' ? 10000 : 10;

    if (amount < minDeposit) {
      return res.status(400).json({
        success: false,
        error: `Minimum deposit is ${minDeposit} ${currency}`
      });
    }

    if (amount > maxDeposit) {
      return res.status(400).json({
        success: false,
        error: `Maximum deposit is ${maxDeposit} ${currency}`
      });
    }

    // Create transaction record
    const transaction = transactionDb.create({
      user_id: user.id,
      type: 'deposit',
      currency,
      amount,
      status: 'pending'
    });

    // In a real application, you would integrate with actual payment processors
    // For this demo, we'll simulate instant deposits
    
    // Generate mock transaction hash
    const transactionHash = `0x${Math.random().toString(16).substring(2, 66)}`;
    
    // Update transaction status and add hash
    transactionDb.updateStatus(transaction.id, 'completed');
    
    // Add to user balance
    userDb.addToBalance(user.id, currency, amount);
    
    // Get updated user data
    const updatedUser = userDb.findById(user.id);

    return res.status(200).json({
      success: true,
      transaction: {
        ...transaction,
        status: 'completed',
        transaction_hash: transactionHash
      },
      balance: {
        usdt: updatedUser?.usdt_balance || 0,
        ltc: updatedUser?.ltc_balance || 0
      },
      message: `Successfully deposited ${amount} ${currency}`
    });
  } catch (error) {
    console.error('Deposit API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
