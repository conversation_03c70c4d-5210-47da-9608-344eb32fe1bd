@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Game grid animations */
  .cell-reveal {
    @apply transition-all duration-300 ease-out;
  }

  .cell-gem {
    @apply bg-gradient-to-br from-green-400 to-emerald-600 shadow-lg;
    animation: pulse-glow 1s ease-out;
  }

  .cell-mine {
    @apply bg-gradient-to-br from-red-500 to-red-700 shadow-lg;
    animation: shake 0.5s ease-in-out;
  }

  .cell-hidden {
    @apply bg-gradient-to-br from-gray-600 to-gray-800 hover:from-gray-500 hover:to-gray-700 transition-all duration-200;
  }

  /* Multiplier display */
  .multiplier-display {
    @apply text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent;
  }

  /* Profit display */
  .profit-positive {
    @apply text-green-400 font-semibold;
  }

  .profit-negative {
    @apply text-red-400 font-semibold;
  }

  /* Button variants */
  .btn-cash-out {
    @apply bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold;
  }

  .btn-start-game {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(75 85 99) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Custom animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(168, 85, 247, 0.4); }
  50% { box-shadow: 0 0 20px rgba(168, 85, 247, 0.8); }
}

/* Loading spinner */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-left: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Game message animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}

@keyframes celebration {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

@keyframes big-win {
  0%, 100% { transform: scale(1) rotate(0deg); }
  20% { transform: scale(1.2) rotate(5deg); }
  40% { transform: scale(1.1) rotate(-3deg); }
  60% { transform: scale(1.3) rotate(3deg); }
  80% { transform: scale(1.1) rotate(-1deg); }
}

@keyframes confetti {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-200px) rotate(720deg);
    opacity: 0;
  }
}

@keyframes screen-shake {
  0%, 100% { transform: translate(0, 0); }
  10% { transform: translate(-2px, -2px); }
  20% { transform: translate(2px, -2px); }
  30% { transform: translate(-2px, 2px); }
  40% { transform: translate(2px, 2px); }
  50% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, -2px); }
  70% { transform: translate(-2px, 2px); }
  80% { transform: translate(2px, 2px); }
  90% { transform: translate(-2px, -2px); }
}
