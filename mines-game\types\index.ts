import React from 'react';

// User and Authentication Types
export interface User {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  usdt_balance: number;
  ltc_balance: number;
  created_at: string;
  updated_at: string;
}

export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (username: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

// Base Game Types
export type GameType = 'mines' | 'dice' | 'crash' | 'plinko' | 'limbo' | 'wheel' | 'blackjack' | 'roulette';

export type GameStatus = 'active' | 'won' | 'lost' | 'cashed_out' | 'cancelled';

export interface BaseGameState {
  id?: number;
  user_id: number;
  game_type: GameType;
  bet_amount: number;
  current_multiplier: number;
  status: GameStatus;
  server_seed: string;
  client_seed: string;
  profit: number;
  created_at?: string;
  updated_at?: string;
}

// Game-specific state interfaces
export interface MinesGameState extends BaseGameState {
  game_type: 'mines';
  grid_size: number;
  mine_count: number;
  revealed_cells: number[];
  mine_positions: number[];
}

export interface DiceGameState extends BaseGameState {
  game_type: 'dice';
  target_number: number;
  roll_under: boolean;
  result?: number;
}

export interface CrashGameState extends BaseGameState {
  game_type: 'crash';
  crash_point?: number;
  cash_out_at?: number;
  auto_cash_out?: number;
}

export interface PlinkoGameState extends BaseGameState {
  game_type: 'plinko';
  risk_level: 'low' | 'medium' | 'high';
  rows: number;
  ball_path?: number[];
  final_multiplier?: number;
}

export interface LimboGameState extends BaseGameState {
  game_type: 'limbo';
  target_multiplier: number;
  result_multiplier?: number;
}

// Union type for all game states
export type GameState = MinesGameState | DiceGameState | CrashGameState | PlinkoGameState | LimboGameState;



// Game Configuration Interface
export interface GameConfig {
  id: GameType;
  name: string;
  description: string;
  icon: string;
  category: 'originals' | 'slots' | 'live' | 'table';
  minBet: number;
  maxBet: number;
  houseEdge: number;
  maxMultiplier: number;
  features: string[];
  isActive: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  // Mines-specific config
  GRID_SIZE?: number;
  MIN_MINES?: number;
  MAX_MINES?: number;
  MIN_BET?: number;
  MAX_BET?: number;
  HOUSE_EDGE?: number;
  BASE_MULTIPLIER?: number;
}

// Game Action Types
export interface GameAction {
  type: string;
  payload?: any;
}

// Generic Game Context Interface
export interface BaseGameContextType<T extends GameState = GameState> {
  gameState: T | null;
  gameHistory: T[];
  loading: boolean;
  error: string | null;
  startGame: (...params: any[]) => Promise<boolean>;
  makeMove?: (params: any) => Promise<any>;
  cashOut?: () => Promise<{ success: boolean; profit: number }>;
  resetGame: () => void;
}

// Specific Game Context Types
export interface MinesGameContextType extends BaseGameContextType<MinesGameState> {
  startGame: (betAmount: number, mineCount: number) => Promise<boolean>;
  revealCell: (cellIndex: number) => Promise<{ hit: boolean; multiplier: number; gameOver: boolean; profit?: number }>;
  cashOut: () => Promise<{ success: boolean; profit: number }>;

  // Mines-specific methods
  switchToMines: () => void;
  loadGameHistory: () => Promise<void>;
  canCashOut: () => boolean;
  getSafeCellsRemaining: () => number;
  getNextMultiplier: () => number;
  isCellRevealed: (cellIndex: number) => boolean;
  isCellMine: (cellIndex: number) => boolean;
  getGameStats: () => {
    gridSize: number;
    mineCount: number;
    revealedCells: number;
    safeCellsRemaining: number;
    currentMultiplier: number;
    profit: number;
    status: GameStatus;
  } | null;
}

export interface DiceGameContextType extends BaseGameContextType<DiceGameState> {
  startGame: (betAmount: number, targetNumber: number, rollUnder: boolean) => Promise<boolean>;
  rollDice: () => Promise<{ result: number; won: boolean; multiplier: number; profit: number }>;

  // Dice-specific methods
  switchToDice: () => void;
  loadGameHistory: () => Promise<void>;
  canRollDice: () => boolean;
  getDiceStats: () => {
    targetNumber: number;
    rollUnder: boolean;
    winChance: number;
    multiplier: number;
    result?: number;
    profit: number;
    status: GameStatus;
  } | null;
  calculateWinChance: (targetNumber: number, rollUnder: boolean) => number;
  calculateMultiplier: (targetNumber: number, rollUnder: boolean) => number;
}

// Union type for all game context types
export type GameContextType = MinesGameContextType | DiceGameContextType;

// Game Provider Interface
export interface GameProviderInterface<T extends GameState = GameState> {
  gameType: GameType;
  config: GameConfig;
  validateGameParams: (params: any) => boolean;
  calculateMultiplier: (gameState: T, params?: any) => number;
  generateGameData: (params: any) => Partial<T>;
  processGameAction: (gameState: T, action: GameAction) => Promise<T>;
}

// Wallet Types
export interface Transaction {
  id: number;
  user_id: number;
  game_id?: number;
  type: 'deposit' | 'withdraw' | 'bet' | 'win';
  currency: 'USDT' | 'LTC';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  transaction_hash?: string;
  created_at: string;
}

// Phase 2: Enhanced Types for Statistics and Leaderboards

export interface UserStatistics {
  id: number;
  user_id: number;
  game_type: GameType;
  total_games: number;
  total_wins: number;
  total_losses: number;
  total_wagered: number;
  total_profit: number;
  biggest_win: number;
  biggest_loss: number;
  highest_multiplier: number;
  current_streak: number;
  best_win_streak: number;
  best_loss_streak: number;
  last_played?: string;
  created_at: string;
  updated_at: string;
}

export interface LeaderboardEntry {
  id: number;
  user_id: number;
  username: string;
  game_type: GameType;
  category: 'profit' | 'multiplier' | 'streak' | 'volume';
  value: number;
  rank_position?: number;
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  period_start: string;
  period_end: string;
  created_at: string;
  updated_at: string;
}

export interface GameSession {
  id: number;
  user_id: number;
  session_start: string;
  session_end?: string;
  total_games: number;
  total_wagered: number;
  total_profit: number;
  games_won: number;
  games_lost: number;
  biggest_win: number;
  biggest_loss: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Achievement {
  id: number;
  code: string;
  name: string;
  description: string;
  icon: string;
  category: 'wins' | 'profit' | 'streak' | 'volume' | 'special';
  requirement_type: 'count' | 'value' | 'streak';
  requirement_value: number;
  reward_type?: 'badge' | 'bonus' | 'title';
  reward_value: number;
  is_active: boolean;
  created_at: string;
}

export interface UserAchievement {
  id: number;
  user_id: number;
  achievement_id: number;
  earned_at: string;
  progress: number;
  is_completed: boolean;
  achievement?: Achievement; // Populated when joined
}

export interface WalletBalance {
  usdt: number;
  ltc: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  username: string;
  email: string;
  password: string;
}

export interface StartGameRequest {
  bet_amount: number;
  mine_count: number;
  client_seed: string;
}

export interface RevealCellRequest {
  game_id: number;
  cell_index: number;
}

export interface DepositRequest {
  currency: 'USDT' | 'LTC';
  amount: number;
}

export interface WithdrawRequest {
  currency: 'USDT' | 'LTC';
  amount: number;
  address: string;
}

// Game Logic Types
export interface CellState {
  index: number;
  revealed: boolean;
  isMine: boolean;
  isGem: boolean;
}



// Sound Types
export type SoundType = 'click' | 'reveal' | 'mine' | 'cashout' | 'win' | 'lose';

// Component Props Types
export interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

export interface CardProps {
  className?: string;
  children: React.ReactNode;
}

// Utility Types
export type Currency = 'USDT' | 'LTC';
export type TransactionType = 'deposit' | 'withdraw' | 'bet' | 'win';
export type TransactionStatus = 'pending' | 'completed' | 'failed';
