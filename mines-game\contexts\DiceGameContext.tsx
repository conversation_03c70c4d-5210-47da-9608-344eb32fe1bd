import React, { createContext, useContext, ReactNode } from 'react';
import { DiceGameState, DiceGameContextType } from '@/types';
import { useUniversalGame } from './UniversalGameContext';
import { useAuth } from './AuthContext';

const DiceGameContext = createContext<DiceGameContextType | undefined>(undefined);

interface DiceGameProviderProps {
  children: ReactNode;
}

export function DiceGameProvider({ children }: DiceGameProviderProps) {
  const universalGame = useUniversalGame();
  const { user } = useAuth();

  // Ensure we're working with a dice game
  const diceGame = universalGame.currentGameType === 'dice'
    ? universalGame.currentGame as DiceGameState | null
    : null;

  const diceHistory = universalGame.gameHistory.filter(
    game => game.game_type === 'dice'
  ) as DiceGameState[];

  /**
   * Start a new dice game
   */
  const startGame = async (betAmount: number, targetNumber: number, rollUnder: boolean): Promise<boolean> => {
    return universalGame.startGame('dice', {
      bet_amount: betAmount,
      target_number: targetNumber,
      roll_under: rollUnder
    });
  };

  /**
   * Roll the dice
   */
  const rollDice = async (): Promise<{ result: number; won: boolean; multiplier: number; profit: number }> => {
    if (!diceGame || diceGame.status !== 'active') {
      throw new Error('No active dice game');
    }

    try {
      const result = await universalGame.makeMove('dice', {
        game_id: diceGame.id
      });

      if (result.success && result.gameState) {
        const updatedGame = result.gameState as DiceGameState;
        return {
          result: updatedGame.result || 0,
          won: updatedGame.status === 'won',
          multiplier: updatedGame.current_multiplier,
          profit: updatedGame.profit
        };
      } else {
        throw new Error(result.error || 'Failed to roll dice');
      }
    } catch (error) {
      console.error('Error rolling dice:', error);
      throw error;
    }
  };

  /**
   * Reset game state
   */
  const resetGame = () => {
    universalGame.resetGame();
  };

  /**
   * Switch to dice game type
   */
  const switchToDice = () => {
    universalGame.switchGame('dice');
  };

  /**
   * Load game history
   */
  const loadGameHistory = async () => {
    await universalGame.loadGameHistory();
  };

  /**
   * Check if player can roll dice
   */
  const canRollDice = (): boolean => {
    return diceGame?.status === 'active' && diceGame.result === undefined;
  };

  /**
   * Get dice game statistics
   */
  const getDiceStats = () => {
    if (!diceGame) return null;

    const winChance = diceGame.roll_under
      ? diceGame.target_number - 1
      : 100 - diceGame.target_number;

    return {
      targetNumber: diceGame.target_number,
      rollUnder: diceGame.roll_under,
      winChance: winChance,
      multiplier: diceGame.current_multiplier,
      result: diceGame.result,
      profit: diceGame.profit,
      status: diceGame.status
    };
  };

  /**
   * Calculate win chance for given parameters
   */
  const calculateWinChance = (targetNumber: number, rollUnder: boolean): number => {
    if (rollUnder) {
      return targetNumber - 1;
    } else {
      return 100 - targetNumber;
    }
  };

  /**
   * Calculate multiplier for given parameters
   */
  const calculateMultiplier = (targetNumber: number, rollUnder: boolean): number => {
    const winChance = calculateWinChance(targetNumber, rollUnder);
    const houseEdge = 0.01; // 1%
    const multiplier = (100 - houseEdge * 100) / winChance;
    return Math.max(1.01, Math.round(multiplier * 10000) / 10000);
  };

  const value: DiceGameContextType = {
    gameState: diceGame,
    gameHistory: diceHistory,
    loading: universalGame.loading,
    error: universalGame.error,
    startGame,
    rollDice,
    resetGame,
    makeMove: rollDice, // Alias for consistency

    // Dice-specific methods
    switchToDice,
    loadGameHistory,
    canRollDice,
    getDiceStats,
    calculateWinChance,
    calculateMultiplier
  };

  return (
    <DiceGameContext.Provider value={value}>
      {children}
    </DiceGameContext.Provider>
  );
}

export function useDiceGame() {
  const context = useContext(DiceGameContext);
  if (context === undefined) {
    throw new Error('useDiceGame must be used within a DiceGameProvider');
  }
  return context;
}
