import { DiceGameState, GameConfig, GameAction } from '@/types';
import { BaseGameProvider, GameActionType, InvalidGameParamsError, GameActionError } from '../BaseGameProvider';

/**
 * Dice Game Provider - Implements the classic dice gambling game
 * Players bet on whether a dice roll (1-100) will be over or under their chosen target number
 */
export class DiceGameProvider extends BaseGameProvider<DiceGameState> {
  public readonly gameType = 'dice' as const;

  public readonly config: GameConfig = {
    id: 'dice',
    name: '<PERSON><PERSON>',
    description: 'Roll the dice and predict if the result will be over or under your chosen number. Simple yet thrilling!',
    icon: '🎲',
    category: 'originals',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.01, // 1%
    maxMultiplier: 9900, // Max possible at 1% win chance
    features: ['Provably Fair', 'Instant Play', 'Custom Multiplier'],
    isActive: true,
    isFeatured: true,
    isNew: true
  };

  // Dice game constants
  private readonly MIN_TARGET = 2;
  private readonly MAX_TARGET = 98;
  private readonly DICE_MIN = 1;
  private readonly DICE_MAX = 100;

  /**
   * Validate dice game parameters
   */
  public validateGameParams(params: {
    betAmount: number;
    targetNumber: number;
    rollUnder: boolean
  }): boolean {
    const { betAmount, targetNumber, rollUnder } = params;

    // Validate base parameters
    if (!this.validateBaseParams(betAmount)) {
      return false;
    }

    // Validate target number
    if (typeof targetNumber !== 'number' ||
        targetNumber < this.MIN_TARGET ||
        targetNumber > this.MAX_TARGET) {
      return false;
    }

    // Validate roll direction
    if (typeof rollUnder !== 'boolean') {
      return false;
    }

    return true;
  }

  /**
   * Calculate multiplier based on win chance
   */
  public calculateMultiplier(gameState: DiceGameState, params?: any): number {
    const { target_number, roll_under } = gameState;

    // Calculate win chance
    const winChance = this.calculateWinChance(target_number, roll_under);

    // Calculate multiplier: (100 - house_edge) / win_chance
    const multiplier = (100 - this.config.houseEdge * 100) / winChance;

    // Round to 4 decimal places and ensure minimum of 1.01
    return Math.max(1.01, Math.round(multiplier * 10000) / 10000);
  }

  /**
   * Calculate win chance percentage
   */
  private calculateWinChance(targetNumber: number, rollUnder: boolean): number {
    if (rollUnder) {
      // Win if roll < target (1 to target-1)
      return targetNumber - 1;
    } else {
      // Win if roll > target (target+1 to 100)
      return 100 - targetNumber;
    }
  }

  /**
   * Generate dice game data
   */
  public generateGameData(params: {
    userId: number;
    betAmount: number;
    targetNumber: number;
    rollUnder: boolean;
    clientSeed?: string
  }): Partial<DiceGameState> {
    const { userId, betAmount, targetNumber, rollUnder, clientSeed } = params;

    if (!this.validateGameParams({ betAmount, targetNumber, rollUnder })) {
      throw new InvalidGameParamsError(this.gameType);
    }

    const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);
    const multiplier = this.calculateMultiplier({
      ...baseData,
      game_type: 'dice',
      target_number: targetNumber,
      roll_under: rollUnder
    } as DiceGameState);

    return {
      ...baseData,
      game_type: 'dice',
      target_number: targetNumber,
      roll_under: rollUnder,
      current_multiplier: multiplier,
      result: undefined // Will be set when dice is rolled
    };
  }

  /**
   * Process dice game actions
   */
  public async processGameAction(gameState: DiceGameState, action: GameAction): Promise<DiceGameState> {
    switch (action.type) {
      case GameActionType.MAKE_MOVE:
        return this.rollDice(gameState);

      default:
        throw new GameActionError(`Unsupported action type: ${action.type}`, this.gameType);
    }
  }

  /**
   * Roll the dice and determine outcome
   */
  private rollDice(gameState: DiceGameState): DiceGameState {
    if (gameState.status !== 'active') {
      throw new GameActionError('Game is not active', this.gameType);
    }

    if (gameState.result !== undefined) {
      throw new GameActionError('Dice already rolled', this.gameType);
    }

    // Generate dice result using provably fair method
    const diceResult = this.generateRandomInt(
      this.DICE_MIN,
      this.DICE_MAX,
      gameState.server_seed,
      gameState.client_seed,
      0 // nonce for dice roll
    );

    // Determine if player won
    const won = this.checkWin(diceResult, gameState.target_number, gameState.roll_under);

    // Calculate profit
    const profit = won ?
      gameState.bet_amount * (gameState.current_multiplier - 1) :
      -gameState.bet_amount;

    return {
      ...gameState,
      result: diceResult,
      status: won ? 'won' : 'lost',
      profit: profit,
      updated_at: new Date().toISOString()
    };
  }

  /**
   * Check if the dice result is a win
   */
  private checkWin(diceResult: number, targetNumber: number, rollUnder: boolean): boolean {
    if (rollUnder) {
      return diceResult < targetNumber;
    } else {
      return diceResult > targetNumber;
    }
  }

  /**
   * Get game statistics for display
   */
  public getGameStats(gameState: DiceGameState) {
    const winChance = this.calculateWinChance(gameState.target_number, gameState.roll_under);

    return {
      targetNumber: gameState.target_number,
      rollUnder: gameState.roll_under,
      winChance: winChance,
      multiplier: gameState.current_multiplier,
      result: gameState.result,
      profit: gameState.profit,
      status: gameState.status
    };
  }
}
