import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { initDatabase, gameDb, userDb } from '@/lib/database';
import { gameFactory } from '@/lib/games/GameFactory';
import { GameType } from '@/types';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  console.log('🚀 API /game/start - Request received, method:', req.method);
  console.log('🚀 API /game/start - User from auth middleware:', user ? { id: user.id, balance: user.usdt_balance } : 'NO USER');

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_type, bet_amount, client_seed, ...gameParams } = req.body;
    console.log('🚀 API /game/start - Full request body:', JSON.stringify(req.body, null, 2));
    console.log('🚀 API /game/start - Extracted values:', { game_type, bet_amount, client_seed, gameParams });
    console.log('🚀 API /game/start - User info:', { id: user.id, balance: user.usdt_balance });

    // Validate game type
    console.log('🚀 API /game/start - Step 1: Validating game type...');
    if (!game_type || typeof game_type !== 'string') {
      console.log('🚀 API /game/start - VALIDATION ERROR: Invalid game type:', game_type);
      return res.status(400).json({
        success: false,
        error: 'Game type is required'
      });
    }
    console.log('🚀 API /game/start - Step 1: Game type validation passed');

    // Validate bet amount
    console.log('🚀 API /game/start - Step 2: Validating bet amount...');
    if (typeof bet_amount !== 'number' || bet_amount <= 0) {
      console.log('🚀 API /game/start - VALIDATION ERROR: Invalid bet amount:', { bet_amount, type: typeof bet_amount });
      return res.status(400).json({
        success: false,
        error: 'Invalid bet amount'
      });
    }
    console.log('🚀 API /game/start - Step 2: Bet amount validation passed');

    // Check if user has sufficient balance
    console.log('🚀 API /game/start - Step 3: Checking user balance...');
    if (bet_amount > user.usdt_balance) {
      console.log('🚀 API /game/start - VALIDATION ERROR: Insufficient balance:', { bet_amount, user_balance: user.usdt_balance });
      return res.status(400).json({
        success: false,
        error: 'Insufficient balance'
      });
    }
    console.log('🚀 API /game/start - Step 3: Balance check passed');

    // Check if user has an active game
    console.log('🚀 API /game/start - Step 4: Checking for active games...');
    const activeGame = gameDb.findActiveByUserId(user.id);
    if (activeGame) {
      console.log('🚀 API /game/start - VALIDATION ERROR: User has active game:', activeGame.id);
      return res.status(400).json({
        success: false,
        error: 'You already have an active game. Please finish it first.'
      });
    }
    console.log('🚀 API /game/start - Step 4: No active games found');

    // Ensure game factory is initialized
    console.log('🚀 API /game/start - Step 5: Initializing game factory...');
    await gameFactory.initialize();

    // Check if game type is available
    console.log('🚀 API /game/start - Step 6: Checking if game type is available...');
    if (!gameFactory.isGameAvailable(game_type as GameType)) {
      console.log('🚀 API /game/start - VALIDATION ERROR: Game type not available:', game_type);
      return res.status(400).json({
        success: false,
        error: `Game type '${game_type}' is not available`
      });
    }
    console.log('🚀 API /game/start - Step 6: Game type is available');

    // Create game using factory
    console.log('🚀 API /game/start - Step 7: Creating game with factory...');

    // Prepare game-specific parameters
    let gameCreationParams: any = {
      userId: user.id,
      betAmount: bet_amount,
      clientSeed: client_seed
    };

    // Add game-specific parameters
    if (game_type === 'mines') {
      gameCreationParams.mineCount = gameParams.mine_count;
    } else if (game_type === 'dice') {
      gameCreationParams.targetNumber = gameParams.target_number;
      gameCreationParams.rollUnder = gameParams.roll_under;
    }

    console.log('🚀 API /game/start - Game creation params:', gameCreationParams);

    const gameResult = await gameFactory.createGame(game_type as GameType, gameCreationParams);

    console.log('🚀 API /game/start - Game creation result:', gameResult);

    if (!gameResult.success || !gameResult.game) {
      console.log('🚀 API /game/start - GAME CREATION ERROR:', gameResult.error);
      return res.status(400).json({
        success: false,
        error: gameResult.error || 'Failed to create game'
      });
    }
    console.log('🚀 API /game/start - Step 7: Game created successfully');

    // Deduct bet amount from user balance
    const newBalance = user.usdt_balance - bet_amount;
    userDb.updateBalance(user.id, 'USDT', newBalance);

    // Save game to database
    const savedGame = gameDb.create(gameResult.game as any);

    // Hide sensitive information for client
    const clientGame = {
      ...savedGame,
      mine_positions: game_type === 'mines' ? [] : savedGame.mine_positions, // Hide mine positions for active games
      server_seed: undefined // Don't send server seed to client
    };

    return res.status(200).json({
      success: true,
      game: clientGame,
      message: 'Game started successfully'
    });

  } catch (error) {
    console.error('🚀 API /game/start - CATCH BLOCK ERROR:', error);
    console.error('🚀 API /game/start - Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
