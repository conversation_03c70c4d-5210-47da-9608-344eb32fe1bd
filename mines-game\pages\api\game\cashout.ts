import { NextApiRequest, NextApiResponse } from 'next';
import { withAuth } from '@/lib/auth';
import { cashOut } from '@/lib/game-logic';
import { initDatabase } from '@/lib/database';

export default withAuth(async (req: NextApiRequest, res: NextApiResponse, user) => {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { game_id } = req.body;

    // Validate input
    if (typeof game_id !== 'number' || game_id <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid game ID'
      });
    }

    const result = await cashOut(game_id, user.id);

    if (result.success) {
      return res.status(200).json({
        success: true,
        profit: result.profit,
        message: 'Successfully cashed out!'
      });
    } else {
      return res.status(400).json({
        success: false,
        error: result.error || 'Failed to cash out'
      });
    }
  } catch (error) {
    console.error('Cash out API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});
