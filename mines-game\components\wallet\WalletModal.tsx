import React, { useState } from 'react';
import { User, Currency } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Wallet, ArrowUpCircle, ArrowDownCircle, Copy, ExternalLink } from 'lucide-react';
import { formatCurrency, API_ENDPOINTS } from '@/lib/utils';

interface WalletModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onBalanceUpdate: () => void;
}

export function WalletModal({ user, isOpen, onClose, onBalanceUpdate }: WalletModalProps) {
  const [activeTab, setActiveTab] = useState('deposit');
  const [currency, setCurrency] = useState<Currency>('USDT');
  const [amount, setAmount] = useState('');
  const [address, setAddress] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleDeposit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      setMessage('Please enter a valid amount');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch(API_ENDPOINTS.WALLET.DEPOSIT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          currency,
          amount: parseFloat(amount),
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`Successfully deposited ${amount} ${currency}`);
        setAmount('');
        onBalanceUpdate();
      } else {
        setMessage(data.error || 'Deposit failed');
      }
    } catch (error) {
      setMessage('Deposit failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      setMessage('Please enter a valid amount');
      return;
    }

    if (!address) {
      setMessage('Please enter a withdrawal address');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await fetch(API_ENDPOINTS.WALLET.WITHDRAW, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          currency,
          amount: parseFloat(amount),
          address,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`Withdrawal of ${amount} ${currency} initiated`);
        setAmount('');
        setAddress('');
        onBalanceUpdate();
      } else {
        setMessage(data.error || 'Withdrawal failed');
      }
    } catch (error) {
      setMessage('Withdrawal failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setMessage('Copied to clipboard!');
    setTimeout(() => setMessage(''), 2000);
  };

  const depositAddresses = {
    USDT: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
    LTC: 'LQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-gray-800 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Wallet className="h-5 w-5 mr-2" />
            Wallet
          </DialogTitle>
        </DialogHeader>

        {/* Balance Display */}
        <Card className="bg-gray-700/50 border-gray-600">
          <CardHeader>
            <CardTitle className="text-lg">Your Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {formatCurrency(user.usdt_balance)}
                </div>
                <div className="text-sm text-gray-300">USDT</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-400">
                  {formatCurrency(user.ltc_balance, 'LTC')}
                </div>
                <div className="text-sm text-gray-300">LTC</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 bg-gray-700">
            <TabsTrigger value="deposit" className="data-[state=active]:bg-gray-600">
              <ArrowDownCircle className="h-4 w-4 mr-2" />
              Deposit
            </TabsTrigger>
            <TabsTrigger value="withdraw" className="data-[state=active]:bg-gray-600">
              <ArrowUpCircle className="h-4 w-4 mr-2" />
              Withdraw
            </TabsTrigger>
          </TabsList>

          <TabsContent value="deposit" className="space-y-4">
            <div className="space-y-4">
              {/* Currency Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Currency</label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={currency === 'USDT' ? 'default' : 'outline'}
                    onClick={() => setCurrency('USDT')}
                    className="w-full"
                  >
                    USDT
                  </Button>
                  <Button
                    variant={currency === 'LTC' ? 'default' : 'outline'}
                    onClick={() => setCurrency('LTC')}
                    className="w-full"
                  >
                    LTC
                  </Button>
                </div>
              </div>

              {/* Amount Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Amount</label>
                <Input
                  type="number"
                  placeholder={`Enter ${currency} amount`}
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="bg-gray-700/50 border-gray-600 text-white"
                />
              </div>

              {/* Deposit Address */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Deposit Address</label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={depositAddresses[currency]}
                    readOnly
                    className="bg-gray-700/50 border-gray-600 text-white text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(depositAddresses[currency])}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-xs text-gray-400">
                  Send {currency} to this address. Minimum deposit: {currency === 'USDT' ? '1' : '0.001'} {currency}
                </div>
              </div>

              <Button
                onClick={handleDeposit}
                disabled={loading || !amount}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                {loading ? 'Processing...' : 'Confirm Deposit'}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="withdraw" className="space-y-4">
            <div className="space-y-4">
              {/* Currency Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Currency</label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={currency === 'USDT' ? 'default' : 'outline'}
                    onClick={() => setCurrency('USDT')}
                    className="w-full"
                  >
                    USDT
                  </Button>
                  <Button
                    variant={currency === 'LTC' ? 'default' : 'outline'}
                    onClick={() => setCurrency('LTC')}
                    className="w-full"
                  >
                    LTC
                  </Button>
                </div>
              </div>

              {/* Amount Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Amount</label>
                <Input
                  type="number"
                  placeholder={`Enter ${currency} amount`}
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="bg-gray-700/50 border-gray-600 text-white"
                />
                <div className="text-xs text-gray-400">
                  Available: {formatCurrency(currency === 'USDT' ? user.usdt_balance : user.ltc_balance, currency)} {currency}
                </div>
              </div>

              {/* Withdrawal Address */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Withdrawal Address</label>
                <Input
                  placeholder={`Enter ${currency} address`}
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  className="bg-gray-700/50 border-gray-600 text-white"
                />
                <div className="text-xs text-gray-400">
                  Minimum withdrawal: {currency === 'USDT' ? '5' : '0.01'} {currency}
                </div>
              </div>

              <Button
                onClick={handleWithdraw}
                disabled={loading || !amount || !address}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                {loading ? 'Processing...' : 'Confirm Withdrawal'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* Message Display */}
        {message && (
          <div className={`text-sm p-3 rounded-md ${
            message.includes('Success') || message.includes('Copied') 
              ? 'bg-green-500/10 text-green-400 border border-green-500/20' 
              : 'bg-red-500/10 text-red-400 border border-red-500/20'
          }`}>
            {message}
          </div>
        )}

        {/* Disclaimer */}
        <div className="text-xs text-gray-400 text-center">
          <p>⚠️ This is a demo. No real transactions are processed.</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
