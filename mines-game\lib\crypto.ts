import crypto from 'crypto';

/**
 * Generate a cryptographically secure random server seed
 */
export function generateServerSeed(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Generate a random client seed
 */
export function generateClientSeed(): string {
  return crypto.randomBytes(16).toString('hex');
}

/**
 * Create SHA-256 hash of combined seeds
 */
export function createGameHash(serverSeed: string, clientSeed: string, nonce: number = 0): string {
  const combined = `${serverSeed}:${clientSeed}:${nonce}`;
  return crypto.createHash('sha256').update(combined).digest('hex');
}

/**
 * Generate mine positions using provably fair algorithm
 * This ensures the mine placement is deterministic based on seeds but unpredictable
 */
export function generateMinePositions(
  serverSeed: string,
  clientSeed: string,
  mineCount: number,
  gridSize: number = 25
): number[] {
  const hash = createGameHash(serverSeed, clientSeed);
  const mines: number[] = [];
  const availablePositions = Array.from({ length: gridSize }, (_, i) => i);
  
  // Use hash bytes to determine mine positions
  let hashIndex = 0;
  
  while (mines.length < mineCount && availablePositions.length > 0) {
    // Get next 4 bytes from hash and convert to number
    const bytes = hash.slice(hashIndex * 8, (hashIndex + 1) * 8);
    const randomValue = parseInt(bytes, 16);
    
    // Use modulo to get position within available positions
    const positionIndex = randomValue % availablePositions.length;
    const position = availablePositions[positionIndex];
    
    mines.push(position);
    availablePositions.splice(positionIndex, 1);
    
    hashIndex++;
    
    // If we run out of hash bytes, create a new hash with incremented nonce
    if (hashIndex * 8 >= hash.length) {
      const newHash = createGameHash(serverSeed, clientSeed, hashIndex);
      const extendedHash = hash + newHash;
      hashIndex = hash.length / 8;
    }
  }
  
  return mines.sort((a, b) => a - b);
}

/**
 * Verify game integrity by recalculating mine positions
 */
export function verifyGameIntegrity(
  serverSeed: string,
  clientSeed: string,
  mineCount: number,
  expectedMines: number[],
  gridSize: number = 25
): boolean {
  const calculatedMines = generateMinePositions(serverSeed, clientSeed, mineCount, gridSize);
  
  if (calculatedMines.length !== expectedMines.length) {
    return false;
  }
  
  return calculatedMines.every((mine, index) => mine === expectedMines[index]);
}

/**
 * Create a hash of the server seed for client verification
 * This allows clients to verify the server seed wasn't changed after the game
 */
export function hashServerSeed(serverSeed: string): string {
  return crypto.createHash('sha256').update(serverSeed).digest('hex');
}

/**
 * Generate a secure random nonce
 */
export function generateNonce(): number {
  return crypto.randomInt(0, 2147483647); // Max 32-bit signed integer
}

/**
 * Create HMAC signature for API security
 */
export function createHMAC(data: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
}

/**
 * Verify HMAC signature
 */
export function verifyHMAC(data: string, signature: string, secret: string): boolean {
  const expectedSignature = createHMAC(data, secret);
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
}

/**
 * Generate a secure session token
 */
export function generateSessionToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Hash password using bcrypt-compatible method
 */
export function hashPassword(password: string): string {
  const bcrypt = require('bcryptjs');
  return bcrypt.hashSync(password, 12);
}

/**
 * Verify password against hash
 */
export function verifyPassword(password: string, hash: string): boolean {
  const bcrypt = require('bcryptjs');
  return bcrypt.compareSync(password, hash);
}

/**
 * Generate JWT secret key
 */
export function generateJWTSecret(): string {
  return crypto.randomBytes(64).toString('hex');
}

/**
 * Calculate game outcome hash for transparency
 */
export function calculateOutcomeHash(
  serverSeed: string,
  clientSeed: string,
  minePositions: number[],
  revealedCells: number[]
): string {
  const outcomeData = {
    serverSeed,
    clientSeed,
    minePositions,
    revealedCells,
    timestamp: Date.now()
  };
  
  return crypto.createHash('sha256').update(JSON.stringify(outcomeData)).digest('hex');
}

/**
 * Generate a unique game ID
 */
export function generateGameId(): string {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(8).toString('hex');
  return `${timestamp}-${random}`;
}

/**
 * Encrypt sensitive data
 */
export function encrypt(text: string, key: string): string {
  const algorithm = 'aes-256-gcm';
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

/**
 * Decrypt sensitive data
 */
export function decrypt(encryptedText: string, key: string): string {
  const algorithm = 'aes-256-gcm';
  const parts = encryptedText.split(':');
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  
  const decipher = crypto.createDecipher(algorithm, key);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}
