import React, { useState, useEffect } from 'react';
import { GameState } from '@/types';
import { Gem, Bomb } from 'lucide-react';
import { soundManager } from '@/lib/sounds';
import { cn } from '@/lib/utils';

interface GameGridProps {
  gameState: GameState | null;
  onCellClick: (cellIndex: number) => void;
  loading: boolean;
}

export function GameGrid({
  gameState,
  onCellClick,
  loading
}: GameGridProps) {
  const [isShaking, setIsShaking] = useState(false);

  // Trigger screen shake when game is lost
  useEffect(() => {
    if (gameState?.status === 'lost') {
      setIsShaking(true);
      const timer = setTimeout(() => setIsShaking(false), 600);
      return () => clearTimeout(timer);
    }
  }, [gameState?.status]);

  const handleCellClick = (index: number) => {
    // Normal game play
    if (!gameState || gameState.status !== 'active' || loading) return;
    const revealedCells = (gameState as any)?.revealed_cells || [];
    if (revealedCells.includes(index)) return;

    soundManager.play('click');
    onCellClick(index);
  };

  const renderCell = (index: number) => {
    if (!gameState) {
      return (
        <button
          key={index}
          className="cell-hidden w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white font-bold transition-all duration-200 disabled:opacity-50"
          disabled
        >
          <span className="text-lg">?</span>
        </button>
      );
    }

    const revealedCells = (gameState as any)?.revealed_cells || [];
    const minePositions = (gameState as any)?.mine_positions || [];
    const isRevealed = revealedCells.includes(index);
    const isMine = minePositions.includes(index);
    const isGameOver = gameState.status !== 'active';

    if (isRevealed) {
      if (isMine) {
        return (
          <button
            key={index}
            className="cell-mine w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white animate-pulse"
            disabled
          >
            <Bomb className="h-6 w-6 sm:h-7 sm:w-7" />
          </button>
        );
      } else {
        return (
          <button
            key={index}
            className="cell-gem w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white animate-pulse"
            disabled
          >
            <Gem className="h-6 w-6 sm:h-7 sm:w-7" />
          </button>
        );
      }
    }

    // Show mines if game is over (lost)
    if (isGameOver && isMine && gameState.status === 'lost') {
      return (
        <button
          key={index}
          className="bg-red-900/50 border border-red-700 w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-red-400 opacity-60"
          disabled
        >
          <Bomb className="h-5 w-5 sm:h-6 sm:w-6" />
        </button>
      );
    }

    return (
      <button
        key={index}
        onClick={() => handleCellClick(index)}
        className={cn(
          "cell-hidden w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-white font-bold transition-all duration-200",
          loading || gameState.status !== 'active'
            ? "opacity-50 cursor-not-allowed"
            : "hover:scale-105 active:scale-95"
        )}
        disabled={loading || gameState.status !== 'active'}
      >
        <span className="text-lg">?</span>
      </button>
    );
  };

  return (
    <div className={cn("flex flex-col items-center space-y-4", isShaking && "animate-screen-shake")}>
      <div className="grid grid-cols-5 gap-2 p-4 bg-gray-900/50 rounded-xl border border-gray-700">
        {Array.from({ length: 25 }, (_, index) => renderCell(index))}
      </div>

      {gameState && (
        <div className="text-center space-y-2">
          <div className="flex justify-center space-x-6 text-sm text-gray-300">
            <span>Mines: {(gameState as any)?.mine_count || 'N/A'}</span>
            <span>Revealed: {((gameState as any)?.revealed_cells || []).length}</span>
            <span>Safe Cells: {25 - ((gameState as any)?.mine_count || 0) - ((gameState as any)?.revealed_cells || []).length}</span>
          </div>


        </div>
      )}
    </div>
  );
}
