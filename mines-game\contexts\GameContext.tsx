import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { GameState, GameContextType } from '@/types';
import { API_ENDPOINTS, generateClientSeed } from '@/lib/utils';
import { useAuth } from './AuthContext';

const GameContext = createContext<GameContextType | undefined>(undefined);

interface GameProviderProps {
  children: ReactNode;
}

export function GameProvider({ children }: GameProviderProps) {
  const [gameState, setGameState] = useState<GameState | null>(null);
  const [gameHistory, setGameHistory] = useState<GameState[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Load active game and history when user changes
  useEffect(() => {
    if (user) {
      loadActiveGame();
      loadGameHistory();
    } else {
      setGameState(null);
      setGameHistory([]);
    }
  }, [user]);

  const loadActiveGame = async () => {
    try {
      const response = await fetch('/api/game/active', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.game) {
          setGameState(data.game);
        }
      }
    } catch (error) {
      console.error('Failed to load active game:', error);
    }
  };

  const loadGameHistory = async () => {
    try {
      const response = await fetch(API_ENDPOINTS.GAME.HISTORY, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.games) {
          setGameHistory(data.games);
        }
      }
    } catch (error) {
      console.error('Failed to load game history:', error);
    }
  };

  const startGame = async (betAmount: number, mineCount: number): Promise<boolean> => {
    try {
      setLoading(true);

      const clientSeed = generateClientSeed();

      const response = await fetch(API_ENDPOINTS.GAME.START, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          bet_amount: betAmount,
          mine_count: mineCount,
          client_seed: clientSeed,
        }),
      });

      const data = await response.json();

      if (data.success && data.game) {
        setGameState(data.game);
        return true;
      } else {
        console.error('Failed to start game:', data.error);
        return false;
      }
    } catch (error) {
      console.error('Start game error:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const revealCell = async (cellIndex: number): Promise<{ hit: boolean; multiplier: number; gameOver: boolean }> => {
    if (!gameState) {
      throw new Error('No active game');
    }

    try {
      setLoading(true);

      const response = await fetch(API_ENDPOINTS.GAME.PICK, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          game_id: gameState.id,
          cell_index: cellIndex,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update game state with new revealed cell
        const updatedGameState = {
          ...gameState,
          revealed_cells: [...gameState.revealed_cells, cellIndex],
          current_multiplier: data.multiplier || gameState.current_multiplier,
          profit: data.profit || gameState.profit,
          status: data.gameOver ? (data.hit ? 'lost' : 'won') : gameState.status,
          // Include mine positions when game is over
          mine_positions: data.gameOver && data.minePositions ? data.minePositions : gameState.mine_positions,
        };

        setGameState(updatedGameState);

        // If game is over, refresh history
        if (data.gameOver) {
          setTimeout(() => {
            loadGameHistory();
            setGameState(null);
          }, 2000);
        }

        return {
          hit: data.hit,
          multiplier: data.multiplier || gameState.current_multiplier,
          gameOver: data.gameOver,
        };
      } else {
        throw new Error(data.error || 'Failed to reveal cell');
      }
    } catch (error) {
      console.error('Reveal cell error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const cashOut = async (): Promise<{ success: boolean; profit: number }> => {
    if (!gameState) {
      throw new Error('No active game');
    }

    try {
      setLoading(true);

      const response = await fetch(API_ENDPOINTS.GAME.CASHOUT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          game_id: gameState.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update game state to cashed out
        const updatedGameState = {
          ...gameState,
          status: 'cashed_out' as const,
        };

        setGameState(updatedGameState);

        // Refresh history and clear active game
        setTimeout(() => {
          loadGameHistory();
          setGameState(null);
        }, 2000);

        return {
          success: true,
          profit: data.profit,
        };
      } else {
        throw new Error(data.error || 'Failed to cash out');
      }
    } catch (error) {
      console.error('Cash out error:', error);
      return {
        success: false,
        profit: 0,
      };
    } finally {
      setLoading(false);
    }
  };

  const value: GameContextType = {
    gameState,
    startGame,
    revealCell,
    cashOut,
    gameHistory,
    loading,
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
}

// DEPRECATED: Use useMinesGame from MinesGameContext instead
export function useGame() {
  // Import and use the new MinesGameContext
  const { useMinesGame } = require('./MinesGameContext');
  try {
    return useMinesGame();
  } catch (error) {
    // If MinesGameContext is not available, throw a more helpful error
    throw new Error('Please use useMinesGame from MinesGameContext instead of useGame from GameContext');
  }
}
