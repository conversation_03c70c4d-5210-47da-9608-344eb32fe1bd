import { MinesGameState, GameAction, GameConfig } from '@/types';
import { BaseGameProvider, GameActionType, InvalidGameParamsError, GameNotActiveError } from '../BaseGameProvider';

/**
 * Mines Game Provider - Implements the classic minesweeper-style gambling game
 */
export class MinesGameProvider extends BaseGameProvider<MinesGameState> {
  public readonly gameType = 'mines' as const;
  
  public readonly config: GameConfig = {
    id: 'mines',
    name: 'Mines',
    description: 'Click tiles to reveal gems while avoiding hidden mines. Cash out anytime to secure your winnings!',
    icon: '💎',
    category: 'originals',
    minBet: 0.01,
    maxBet: 1000,
    houseEdge: 0.04, // 4%
    maxMultiplier: 1000,
    features: ['Provably Fair', 'Instant Play', 'Auto Cashout', 'Custom Risk'],
    isActive: true,
    isFeatured: true,
    isNew: false
  };

  private readonly GRID_SIZE = 25;
  private readonly MIN_MINES = 1;
  private readonly MAX_MINES = 24;

  /**
   * Validate mines game parameters
   */
  public validateGameParams(params: { betAmount: number; mineCount: number }): boolean {
    const { betAmount, mineCount } = params;

    // Validate base parameters
    if (!this.validateBaseParams(betAmount)) {
      return false;
    }

    // Validate mine count
    if (typeof mineCount !== 'number' || 
        mineCount < this.MIN_MINES || 
        mineCount > this.MAX_MINES) {
      return false;
    }

    return true;
  }

  /**
   * Calculate multiplier based on mines and revealed safe cells
   */
  public calculateMultiplier(gameState: MinesGameState, params?: { revealedCells?: number }): number {
    const revealedSafeCells = params?.revealedCells ?? gameState.revealed_cells.length;
    
    if (revealedSafeCells === 0) return 1.0;

    const totalSafeCells = this.GRID_SIZE - gameState.mine_count;
    const remainingSafeCells = totalSafeCells - revealedSafeCells;

    if (remainingSafeCells <= 0) return 1.0;

    // Calculate probability-based multiplier
    let multiplier = 1.0;
    
    for (let i = 0; i < revealedSafeCells; i++) {
      const safeCellsAtStep = totalSafeCells - i;
      const totalCellsAtStep = this.GRID_SIZE - i;
      const probability = safeCellsAtStep / totalCellsAtStep;
      multiplier *= (1 / probability);
    }

    // Apply house edge
    multiplier = this.applyHouseEdge(multiplier);

    // Ensure multiplier doesn't exceed maximum
    return Math.min(multiplier, this.config.maxMultiplier);
  }

  /**
   * Generate mines game data
   */
  public generateGameData(params: { 
    userId: number; 
    betAmount: number; 
    mineCount: number; 
    clientSeed?: string 
  }): Partial<MinesGameState> {
    const { userId, betAmount, mineCount, clientSeed } = params;

    if (!this.validateGameParams({ betAmount, mineCount })) {
      throw new InvalidGameParamsError(this.gameType);
    }

    const baseData = this.generateBaseGameData(userId, betAmount, clientSeed);
    const minePositions = this.generateMinePositions(
      baseData.server_seed!,
      baseData.client_seed!,
      mineCount
    );

    return {
      ...baseData,
      game_type: 'mines',
      grid_size: this.GRID_SIZE,
      mine_count: mineCount,
      revealed_cells: [],
      mine_positions: minePositions
    };
  }

  /**
   * Process game actions (reveal cell, cash out, etc.)
   */
  public async processGameAction(gameState: MinesGameState, action: GameAction): Promise<MinesGameState> {
    this.logGameAction(gameState, action);

    if (!this.isGameActive(gameState)) {
      throw new GameNotActiveError(this.gameType);
    }

    switch (action.type) {
      case GameActionType.MAKE_MOVE:
        return this.processRevealCell(gameState, action.payload.cellIndex);
      
      case GameActionType.CASH_OUT:
        return this.processCashOut(gameState);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Process revealing a cell
   */
  private processRevealCell(gameState: MinesGameState, cellIndex: number): MinesGameState {
    // Validate cell index
    if (cellIndex < 0 || cellIndex >= this.GRID_SIZE) {
      throw new Error('Invalid cell index');
    }

    // Check if cell is already revealed
    if (gameState.revealed_cells.includes(cellIndex)) {
      throw new Error('Cell already revealed');
    }

    const newRevealedCells = [...gameState.revealed_cells, cellIndex];
    const hitMine = gameState.mine_positions.includes(cellIndex);

    if (hitMine) {
      // Game over - hit mine
      return {
        ...gameState,
        revealed_cells: newRevealedCells,
        status: 'lost',
        profit: -gameState.bet_amount,
        current_multiplier: 0
      };
    } else {
      // Safe cell revealed
      const newMultiplier = this.calculateMultiplier(gameState, { revealedCells: newRevealedCells.length });
      const profit = this.calculateProfit(gameState.bet_amount, newMultiplier);

      // Check if all safe cells are revealed (auto win)
      const totalSafeCells = this.GRID_SIZE - gameState.mine_count;
      const isGameComplete = newRevealedCells.length === totalSafeCells;

      return {
        ...gameState,
        revealed_cells: newRevealedCells,
        current_multiplier: newMultiplier,
        profit: profit,
        status: isGameComplete ? 'won' : 'active'
      };
    }
  }

  /**
   * Process cash out action
   */
  private processCashOut(gameState: MinesGameState): MinesGameState {
    if (gameState.revealed_cells.length === 0) {
      throw new Error('Cannot cash out without revealing any cells');
    }

    const profit = this.calculateProfit(gameState.bet_amount, gameState.current_multiplier);

    return {
      ...gameState,
      status: 'cashed_out',
      profit: profit
    };
  }

  /**
   * Generate mine positions using provably fair method
   */
  private generateMinePositions(serverSeed: string, clientSeed: string, mineCount: number): number[] {
    const positions: number[] = [];
    const availablePositions = Array.from({ length: this.GRID_SIZE }, (_, i) => i);
    
    // Shuffle available positions using provably fair randomness
    const shuffledPositions = this.shuffleArray(availablePositions, serverSeed, clientSeed);
    
    // Take first mineCount positions
    return shuffledPositions.slice(0, mineCount).sort((a, b) => a - b);
  }

  /**
   * Get safe cells remaining
   */
  public getSafeCellsRemaining(gameState: MinesGameState): number {
    const totalSafeCells = this.GRID_SIZE - gameState.mine_count;
    return totalSafeCells - gameState.revealed_cells.length;
  }

  /**
   * Get next multiplier if safe cell is revealed
   */
  public getNextMultiplier(gameState: MinesGameState): number {
    if (!this.isGameActive(gameState)) return gameState.current_multiplier;
    
    const nextRevealedCount = gameState.revealed_cells.length + 1;
    return this.calculateMultiplier(gameState, { revealedCells: nextRevealedCount });
  }

  /**
   * Check if player can cash out
   */
  public canCashOut(gameState: MinesGameState): boolean {
    return this.isGameActive(gameState) && gameState.revealed_cells.length > 0;
  }
}
