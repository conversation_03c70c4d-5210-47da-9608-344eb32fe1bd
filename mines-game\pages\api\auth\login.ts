import { NextApiRequest, NextApiResponse } from 'next';
import { loginUser, setAuth<PERSON>ookie, checkRateLimit, getClientIP } from '@/lib/auth';
import { initDatabase } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Initialize database
  initDatabase();

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Rate limiting
    const clientIP = getClientIP(req);
    if (!checkRateLimit(clientIP, 10, 15 * 60 * 1000)) {
      return res.status(429).json({
        success: false,
        error: 'Too many login attempts. Please try again later.'
      });
    }

    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    const result = await loginUser(email, password);

    if (result.success && result.user && result.token) {
      setAuthCookie(res, result.token);

      return res.status(200).json({
        success: true,
        user: result.user,
        message: 'Login successful'
      });
    } else {
      return res.status(401).json({
        success: false,
        error: result.error || 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Login API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
