import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { formatCurrency } from '@/lib/utils';

interface ChartDataPoint {
  gameId: number;
  timestamp: string;
  profit: number;
  cumulativeProfit: number;
  betAmount: number;
  multiplier: number;
  gameType: string;
}

interface ProfitLossChartProps {
  data: ChartDataPoint[];
  className?: string;
}

export function ProfitLossChart({ data, className = '' }: ProfitLossChartProps) {
  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg">
          <p className="text-gray-300 text-sm mb-1">Game #{data.gameId}</p>
          <p className="text-white font-semibold">
            Cumulative: {formatCurrency(data.cumulativeProfit)}
          </p>
          <p className={`text-sm ${data.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            This game: {data.profit >= 0 ? '+' : ''}{formatCurrency(data.profit)}
          </p>
          <p className="text-gray-400 text-xs">
            Bet: {formatCurrency(data.betAmount)} • {data.multiplier}x
          </p>
        </div>
      );
    }
    return null;
  };

  // Format X-axis labels to show game numbers
  const formatXAxisLabel = (tickItem: any, index: number) => {
    return `#${tickItem}`;
  };

  // Determine line color based on final profit
  const finalProfit = data.length > 0 ? data[data.length - 1].cumulativeProfit : 0;
  const lineColor = finalProfit >= 0 ? '#10b981' : '#ef4444'; // green-500 or red-500

  if (data.length === 0) {
    return (
      <div className={`flex items-center justify-center h-64 bg-gray-900/50 rounded-lg border border-gray-700 ${className}`}>
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">No games played yet</div>
          <div className="text-gray-500 text-sm">Start playing to see your profit/loss chart</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-900/50 rounded-lg border border-gray-700 p-4 ${className}`}>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="gameId"
              tickFormatter={formatXAxisLabel}
              stroke="#9ca3af"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <YAxis 
              tickFormatter={(value) => formatCurrency(value, true)}
              stroke="#9ca3af"
              fontSize={12}
              tickLine={false}
              axisLine={false}
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={0} stroke="#6b7280" strokeDasharray="2 2" />
            <Line
              type="monotone"
              dataKey="cumulativeProfit"
              stroke={lineColor}
              strokeWidth={2}
              dot={{ fill: lineColor, strokeWidth: 0, r: 3 }}
              activeDot={{ r: 5, stroke: lineColor, strokeWidth: 2, fill: '#1f2937' }}
              connectNulls={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
