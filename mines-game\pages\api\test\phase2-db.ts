import { NextApiRequest, NextApiResponse } from 'next';
import { initDatabase, userStatsDb, leaderboardDb, gameSessionDb } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🧪 Testing Phase 2 Database Schema Enhancement...');

    // Initialize database
    const db = initDatabase();
    console.log('✅ Database initialized successfully');

    // Test if new tables exist
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN (
        'user_statistics', 'leaderboards', 'game_sessions', 
        'achievements', 'user_achievements'
      )
    `).all();

    console.log('📋 Phase 2 Tables Created:');
    tables.forEach((table: any) => {
      console.log(`  ✅ ${table.name}`);
    });

    const results = {
      tablesCreated: tables.length,
      expectedTables: 5,
      tables: tables.map((t: any) => t.name),
      tests: []
    };

    // Test user statistics operations
    console.log('🔍 Testing User Statistics Operations...');
    try {
      const testStat = userStatsDb.create(1, 'mines');
      results.tests.push({ name: 'User Statistics Creation', status: 'success' });
      console.log('  ✅ User statistics creation works');
    } catch (error: any) {
      results.tests.push({ name: 'User Statistics Creation', status: 'failed', error: error.message });
      console.log('  ❌ User statistics creation failed:', error.message);
    }

    // Test leaderboard operations
    console.log('🏆 Testing Leaderboard Operations...');
    try {
      const leaderboardUpdate = leaderboardDb.updateEntry(1, 'testuser', 'mines', 'profit', 100.50, 'daily');
      results.tests.push({ name: 'Leaderboard Update', status: 'success' });
      console.log('  ✅ Leaderboard update works');
    } catch (error: any) {
      results.tests.push({ name: 'Leaderboard Update', status: 'failed', error: error.message });
      console.log('  ❌ Leaderboard update failed:', error.message);
    }

    // Test game session operations
    console.log('🎮 Testing Game Session Operations...');
    try {
      const session = gameSessionDb.startSession(1);
      results.tests.push({ name: 'Game Session Creation', status: 'success' });
      console.log('  ✅ Game session creation works');
    } catch (error: any) {
      results.tests.push({ name: 'Game Session Creation', status: 'failed', error: error.message });
      console.log('  ❌ Game session creation failed:', error.message);
    }

    console.log('✨ Phase 2 Database Schema Enhancement Test Complete!');

    return res.status(200).json({
      success: true,
      message: 'Phase 2 Database Schema Enhancement Test Complete!',
      results
    });

  } catch (error: any) {
    console.error('❌ Database test failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Database test failed',
      details: error.message
    });
  }
}
