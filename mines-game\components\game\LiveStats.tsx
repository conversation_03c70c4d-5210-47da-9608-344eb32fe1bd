import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  Target,
  DollarSign,
  Trophy,
  X
} from 'lucide-react';
import { formatCurrency, API_ENDPOINTS, SessionStorage } from '@/lib/utils';
import { ProfitLossChart } from './ProfitLossChart';

interface StatsData {
  totalProfit: number;
  totalWagered: number;
  totalWins: number;
  totalLosses: number;
  winRate: number;
  chartData: Array<{
    gameId: number;
    timestamp: string;
    profit: number;
    cumulativeProfit: number;
    betAmount: number;
    multiplier: number;
    gameType: string;
  }>;
}

interface LiveStatsProps {
  isOpen: boolean;
  onClose: () => void;
  refreshTrigger?: number; // Optional trigger to force refresh
}

export function LiveStats({ isOpen, onClose, refreshTrigger }: LiveStatsProps) {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeFilter, setTimeFilter] = useState<string>('all');

  useEffect(() => {
    if (isOpen) {
      loadStats();
    }
  }, [isOpen]);

  // Refresh stats when refreshTrigger changes (only if modal is open)
  useEffect(() => {
    if (isOpen && refreshTrigger && refreshTrigger > 0) {
      loadStats();
    }
  }, [refreshTrigger, isOpen]);

  const loadStats = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get session start time for filtering
      const sessionStartTime = SessionStorage.getSessionStartTime();
      console.log('📊 Loading stats with session start time:', sessionStartTime);

      const url = new URL('/api/game/stats', window.location.origin);
      if (sessionStartTime) {
        url.searchParams.append('sessionStartTime', sessionStartTime);
      }

      const response = await fetch(url.toString(), {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStats(data.stats);
          setError(null);
        } else {
          console.error('Stats API returned error:', data.error);
          setError(data.error || 'Failed to load stats');
          setStats(null);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('Stats API HTTP error:', response.status, errorData);
        setError(errorData.error || `HTTP ${response.status}: Failed to load stats`);
        setStats(null);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
      setError(error instanceof Error ? error.message : 'Network error occurred');
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    try {
      // Call API to reset user's game history
      const response = await fetch('/api/game/reset-stats', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        // Reset session storage and reload stats
        SessionStorage.resetSession();
        loadStats();
      } else {
        setError('Failed to reset stats');
      }
    } catch (error) {
      console.error('Reset error:', error);
      setError('Failed to reset stats');
    }
  };

  // Filter chart data based on game count selection
  const getFilteredChartData = () => {
    if (!stats?.chartData) return [];

    const data = stats.chartData;

    switch (timeFilter) {
      case '24h': // Last 5 games
        return data.slice(-5);
      case '7d': // Last 10 games
        return data.slice(-10);
      case '30d': // Last 30 games
        return data.slice(-30);
      case 'all':
      default: // All session
        return data;
    }
  };

  const filteredData = getFilteredChartData();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl bg-gray-800 border-gray-700 text-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Session Stats
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={loading}
                className="border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white"
                title="Reset session stats"
              >
                <RotateCcw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {loading && !stats ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
            <div className="text-gray-300 mt-2">Loading stats...</div>
          </div>
        ) : stats ? (
          <div className="space-y-6">
            {/* Stats Overview */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="bg-gray-700/30 border-gray-600">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-green-400" />
                    <div className="text-xs text-gray-400">Session Profit</div>
                  </div>
                  <div className={`text-lg font-bold ${stats.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {stats.totalProfit >= 0 ? '+' : ''}{formatCurrency(stats.totalProfit)}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-700/30 border-gray-600">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-blue-400" />
                    <div className="text-xs text-gray-400">Session Wagered</div>
                  </div>
                  <div className="text-lg font-bold text-white">
                    {formatCurrency(stats.totalWagered)}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-700/30 border-gray-600">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Trophy className="h-4 w-4 text-yellow-400" />
                    <div className="text-xs text-gray-400">Session Wins</div>
                  </div>
                  <div className="text-lg font-bold text-green-400">
                    {stats.totalWins}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-700/30 border-gray-600">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <X className="h-4 w-4 text-red-400" />
                    <div className="text-xs text-gray-400">Session Losses</div>
                  </div>
                  <div className="text-lg font-bold text-red-400">
                    {stats.totalLosses}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Chart Section */}
            <Card className="bg-gray-700/30 border-gray-600">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Session Profit/Loss Chart</CardTitle>
                  <Select value={timeFilter} onValueChange={setTimeFilter}>
                    <SelectTrigger className="w-32 bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-700 border-gray-600">
                      <SelectItem value="all">All Session</SelectItem>
                      <SelectItem value="30d">Last 30 Games</SelectItem>
                      <SelectItem value="7d">Last 10 Games</SelectItem>
                      <SelectItem value="24h">Last 5 Games</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <ProfitLossChart data={filteredData} />
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-red-400 mb-2">Failed to load stats</div>
            <div className="text-gray-400 text-sm mb-4">
              {error || 'Please try again.'}
            </div>
            <Button
              onClick={handleReset}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {loading ? 'Loading...' : 'Reset & Retry'}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
