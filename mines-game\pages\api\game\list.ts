import { NextApiRequest, NextApiResponse } from 'next';
import { gameFactory } from '@/lib/games/GameFactory';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Ensure game factory is initialized
    await gameFactory.initialize();

    const { category, featured, active } = req.query;

    let games = gameFactory.getAllGames();

    // Apply filters
    if (category && typeof category === 'string') {
      games = gameFactory.getGamesByCategory(category as any);
    }

    if (featured === 'true') {
      games = games.filter(game => game.isFeatured);
    }

    if (active === 'true') {
      games = games.filter(game => game.isActive);
    }

    // Sort games by featured status, then by name
    games.sort((a, b) => {
      if (a.isFeatured && !b.isFeatured) return -1;
      if (!a.isFeatured && b.isFeatured) return 1;
      return a.name.localeCompare(b.name);
    });

    return res.status(200).json({
      success: true,
      games,
      total: games.length
    });

  } catch (error) {
    console.error('List games API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
