import { GameType, GameConfig, GameProviderInterface } from '@/types';

/**
 * Game Registry - Central hub for all game configurations and providers
 */
class GameRegistry {
  private games: Map<GameType, GameConfig> = new Map();
  private providers: Map<GameType, GameProviderInterface> = new Map();

  /**
   * Register a new game with its configuration and provider
   */
  registerGame(config: GameConfig, provider: GameProviderInterface): void {
    if (this.games.has(config.id)) {
      console.warn(`Game ${config.id} is already registered. Overwriting...`);
    }
    
    this.games.set(config.id, config);
    this.providers.set(config.id, provider);
    
    console.log(`✅ Registered game: ${config.name} (${config.id})`);
  }

  /**
   * Get game configuration by type
   */
  getGameConfig(gameType: GameType): GameConfig | undefined {
    return this.games.get(gameType);
  }

  /**
   * Get game provider by type
   */
  getGameProvider(gameType: GameType): GameProviderInterface | undefined {
    return this.providers.get(gameType);
  }

  /**
   * Get all registered games
   */
  getAllGames(): GameConfig[] {
    return Array.from(this.games.values());
  }

  /**
   * Get games by category
   */
  getGamesByCategory(category: GameConfig['category']): GameConfig[] {
    return this.getAllGames().filter(game => game.category === category);
  }

  /**
   * Get active games only
   */
  getActiveGames(): GameConfig[] {
    return this.getAllGames().filter(game => game.isActive);
  }

  /**
   * Get featured games
   */
  getFeaturedGames(): GameConfig[] {
    return this.getAllGames().filter(game => game.isFeatured && game.isActive);
  }

  /**
   * Get new games
   */
  getNewGames(): GameConfig[] {
    return this.getAllGames().filter(game => game.isNew && game.isActive);
  }

  /**
   * Search games by name or description
   */
  searchGames(query: string): GameConfig[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllGames().filter(game => 
      game.name.toLowerCase().includes(lowercaseQuery) ||
      game.description.toLowerCase().includes(lowercaseQuery) ||
      game.features.some(feature => feature.toLowerCase().includes(lowercaseQuery))
    );
  }

  /**
   * Check if a game type is registered
   */
  isGameRegistered(gameType: GameType): boolean {
    return this.games.has(gameType);
  }

  /**
   * Get game statistics
   */
  getRegistryStats(): {
    totalGames: number;
    activeGames: number;
    gamesByCategory: Record<string, number>;
  } {
    const allGames = this.getAllGames();
    const activeGames = this.getActiveGames();
    
    const gamesByCategory = allGames.reduce((acc, game) => {
      acc[game.category] = (acc[game.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalGames: allGames.length,
      activeGames: activeGames.length,
      gamesByCategory
    };
  }

  /**
   * Validate game configuration
   */
  private validateGameConfig(config: GameConfig): boolean {
    const requiredFields = ['id', 'name', 'description', 'category', 'minBet', 'maxBet'];
    
    for (const field of requiredFields) {
      if (!(field in config)) {
        console.error(`Game config missing required field: ${field}`);
        return false;
      }
    }

    if (config.minBet >= config.maxBet) {
      console.error('minBet must be less than maxBet');
      return false;
    }

    if (config.houseEdge < 0 || config.houseEdge > 1) {
      console.error('houseEdge must be between 0 and 1');
      return false;
    }

    return true;
  }

  /**
   * Unregister a game (for testing or maintenance)
   */
  unregisterGame(gameType: GameType): boolean {
    const hasGame = this.games.has(gameType);
    this.games.delete(gameType);
    this.providers.delete(gameType);
    
    if (hasGame) {
      console.log(`🗑️ Unregistered game: ${gameType}`);
    }
    
    return hasGame;
  }

  /**
   * Clear all registered games (for testing)
   */
  clear(): void {
    this.games.clear();
    this.providers.clear();
    console.log('🧹 Cleared all registered games');
  }
}

// Export singleton instance
export const gameRegistry = new GameRegistry();

// Export the class for testing
export { GameRegistry };
